/* TipTap Editor Styles */

.ProseMirror {
  outline: none;
}

.ProseMirror p {
  margin-bottom: 1em;
}

.ProseMirror h1 {
  font-size: 2em;
  font-weight: bold;
  margin-bottom: 0.5em;
  margin-top: 1em;
}

.ProseMirror h2 {
  font-size: 1.5em;
  font-weight: bold;
  margin-bottom: 0.5em;
  margin-top: 1em;
}

.ProseMirror h3 {
  font-size: 1.25em;
  font-weight: bold;
  margin-bottom: 0.5em;
  margin-top: 1em;
}

.ProseMirror ul,
.ProseMirror ol {
  padding-left: 1.5em;
  margin-bottom: 1em;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

.ProseMirror li {
  margin-bottom: 0.5em;
}

.ProseMirror a {
  color: #3b82f6;
  text-decoration: underline;
}

.ProseMirror blockquote {
  border-left: 3px solid #e5e7eb;
  padding-left: 1em;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  border-radius: 0.375rem;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 2em 0;
}

.ProseMirror code {
  background-color: #f3f4f6;
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-family: monospace;
}

.ProseMirror pre {
  background-color: #1f2937;
  color: #e5e7eb;
  padding: 1em;
  border-radius: 0.375rem;
  font-family: monospace;
  overflow-x: auto;
  margin-bottom: 1em;
}

.ProseMirror pre code {
  background-color: transparent;
  padding: 0;
  color: inherit;
}

.ProseMirror mark {
  background-color: #fef3c7;
  padding: 0.1em 0.2em;
  border-radius: 0.25em;
}

/* Enhanced responsive video styling for TipTap editor */
.ProseMirror [data-youtube-video] {
  position: relative;
  width: 100%;
  aspect-ratio: var(--video-aspect-ratio, 16 / 9);
  overflow: hidden;
  margin: var(--video-margin, 1.5rem 0);
  border-radius: var(--video-border-radius, 0.75rem);
  box-shadow: var(--video-shadow, 0 4px 12px rgba(0, 0, 0, 0.1));
  background: linear-gradient(135deg, hsl(var(--muted)/0.1) 0%, hsl(var(--muted)/0.05) 100%);
  transition: var(--video-transition, all 0.3s cubic-bezier(0.4, 0, 0.2, 1));
}

.ProseMirror [data-youtube-video]:hover {
  box-shadow: var(--video-shadow-hover, 0 8px 24px rgba(0, 0, 0, 0.15));
  transform: translateY(-2px);
}

.ProseMirror [data-youtube-video] iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: var(--video-border-radius, 0.75rem);
  transition: var(--video-transition, all 0.3s cubic-bezier(0.4, 0, 0.2, 1));
}

/* Mobile optimizations for TipTap videos */
@media (max-width: 768px) {
  .ProseMirror [data-youtube-video] {
    aspect-ratio: var(--video-aspect-ratio-mobile, 16 / 10);
    margin: 1rem 0;
    border-radius: 0.5rem;
  }

  .ProseMirror [data-youtube-video] iframe {
    border-radius: 0.5rem;
  }

  .ProseMirror [data-youtube-video]:hover {
    transform: none; /* Disable hover effects on mobile */
  }
}

@media (max-width: 480px) {
  .ProseMirror [data-youtube-video] {
    aspect-ratio: 16 / 11; /* Slightly taller for very small screens */
    margin: 0.75rem 0;
    border-radius: 0.375rem;
  }

  .ProseMirror [data-youtube-video] iframe {
    border-radius: 0.375rem;
  }
}

/* Dark mode adjustments for TipTap videos */
.dark .ProseMirror [data-youtube-video] {
  background: linear-gradient(135deg, hsl(var(--muted)/0.2) 0%, hsl(var(--muted)/0.1) 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .ProseMirror [data-youtube-video]:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

/* Text alignment */
.ProseMirror .text-left {
  text-align: left;
}

.ProseMirror .text-center {
  text-align: center;
}

.ProseMirror .text-right {
  text-align: right;
}

/* Placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #9ca3af;
  pointer-events: none;
  height: 0;
}

/* Editor focus styles */
.ProseMirror:focus {
  outline: none;
}
