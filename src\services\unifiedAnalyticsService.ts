import { supabase } from '@/integrations/supabase/client';
import {
  UnifiedStudentData,
  SchoolUnifiedData,
  UnifiedAnalyticsOverview
} from '@/types/unified-analytics';
import { StudentProfile, TestSubmissionSummary } from '@/types/test-analytics';
import { UserDemographicResponse, DemographicResponse } from '@/types/demographic';

/**
 * Get unified analytics combining demographic and test data
 */
export const getUnifiedAnalytics = async (): Promise<UnifiedAnalyticsOverview> => {
  try {
    console.log('Fetching unified analytics...');

    // Fetch all demographic responses
    const { data: demographicResponses, error: demoError } = await supabase
      .from('user_demographic_responses')
      .select('*')
      .order('completed_at', { ascending: false });

    if (demoError) {
      console.error('Error fetching demographic responses:', demoError);
      throw demoError;
    }

    // Fetch all test responses
    const { data: testResponses, error: testError } = await supabase
      .from('module_test_responses')
      .select('id, test_id, user_id, responses, created_at, updated_at');

    if (testError) {
      console.error('Error fetching test responses:', testError);
      throw testError;
    }

    // Get all unique user IDs from both datasets
    const demoUserIds = new Set(demographicResponses?.map(r => r.user_id) || []);
    const testUserIds = new Set(testResponses?.map(r => r.user_id) || []);
    const allUserIds = [...new Set([...demoUserIds, ...testUserIds])];

    if (allUserIds.length === 0) {
      return {
        totalStudents: 0,
        studentsWithDemographics: 0,
        studentsWithTests: 0,
        studentsWithBoth: 0,
        totalSchools: 0,
        schools: [],
        overallDemographicBreakdown: {
          by_country: {},
          by_gender: {},
          by_role: {},
          by_age: {}
        },
        overallTestBreakdown: {
          totalTests: 0,
          preTests: 0,
          postTests: 0,
          averageTestsPerStudent: 0
        }
      };
    }

    // Fetch user profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, first_name, last_name')
      .in('id', allUserIds);

    if (profilesError) {
      console.warn('Error fetching profiles:', profilesError);
    }

    // For auth users, we'll use a different approach since auth.users is not directly accessible
    // We'll get the current user's email and use a placeholder for others
    const currentUser = await supabase.auth.getUser();
    const currentUserEmail = currentUser.data.user?.email;

    // Create user profile map
    const profilesMap = new Map();
    profiles?.forEach(profile => {
      profilesMap.set(profile.id, profile);
    });

    // Get test details for creating test summaries
    const testIds = [...new Set(testResponses?.map(r => r.test_id) || [])];
    const { data: tests, error: testsError } = await supabase
      .from('module_tests')
      .select(`
        id,
        title,
        type,
        module_id,
        modules (
          id,
          title,
          course_id,
          courses (
            id,
            title
          )
        )
      `)
      .in('id', testIds);

    if (testsError) {
      console.warn('Error fetching test details:', testsError);
    }

    const testsMap = new Map();
    tests?.forEach(test => {
      testsMap.set(test.id, test);
    });

    // Process each user to create unified data
    const unifiedStudents: UnifiedStudentData[] = [];
    const schoolGroups: Record<string, UnifiedStudentData[]> = {};

    allUserIds.forEach(userId => {
      const profile = profilesMap.get(userId);
      const demoResponse = demographicResponses?.find(r => r.user_id === userId);
      const userTestResponses = testResponses?.filter(r => r.user_id === userId) || [];

      // Create student profile - use current user email if available, otherwise use placeholder
      const student: StudentProfile = {
        id: userId,
        email: (userId === currentUser.data.user?.id ? currentUserEmail : demoResponse?.responses?.email || '<EMAIL>') || 'Unknown',
        firstName: profile?.first_name || undefined,
        lastName: profile?.last_name || undefined,
        fullName: profile ? `${profile.first_name || ''} ${profile.last_name || ''}`.trim() : 'Unknown'
      };

      // Process demographic data
      const demographicData = demoResponse ? {
        responses: demoResponse.responses,
        completedAt: demoResponse.completed_at,
        school: demoResponse.responses.university || 'Unknown/Not Specified'
      } : null;

      // Process test results
      const testResults: TestSubmissionSummary[] = userTestResponses.map(response => {
        const test = testsMap.get(response.test_id);
        const module = test?.modules;
        const course = module?.courses;

        return {
          id: response.id,
          testId: response.test_id,
          testTitle: test?.title || 'Unknown Test',
          testType: test?.type || 'post_test',
          moduleId: module?.id || '',
          moduleTitle: module?.title || 'Unknown Module',
          courseId: course?.id || '',
          courseTitle: course?.title || 'Unknown Course',
          submittedAt: response.created_at,
          responseCount: JSON.parse(response.responses || '[]').length
        };
      });

      const unifiedStudent: UnifiedStudentData = {
        student,
        demographicData,
        testResults,
        totalTestsCompleted: testResults.length
      };

      unifiedStudents.push(unifiedStudent);

      // Group by school
      const schoolName = demographicData?.school || 'Unknown/Not Specified';
      if (!schoolGroups[schoolName]) {
        schoolGroups[schoolName] = [];
      }
      schoolGroups[schoolName].push(unifiedStudent);
    });

    // Process school groups to create school analytics
    const schools: SchoolUnifiedData[] = Object.entries(schoolGroups).map(([schoolName, students]) => {
      const studentsWithDemographics = students.filter(s => s.demographicData !== null).length;
      const studentsWithTests = students.filter(s => s.testResults.length > 0).length;
      const studentsWithBoth = students.filter(s => s.demographicData !== null && s.testResults.length > 0).length;

      // Calculate demographic breakdown for this school
      const demographicBreakdown = {
        by_country: {} as Record<string, number>,
        by_gender: {} as Record<string, number>,
        by_role: {} as Record<string, number>,
        by_age: {} as Record<string, number>
      };

      students.forEach(student => {
        if (student.demographicData) {
          const responses = student.demographicData.responses;
          
          // Country
          const country = responses.country || 'Unknown';
          demographicBreakdown.by_country[country] = (demographicBreakdown.by_country[country] || 0) + 1;

          // Gender
          const gender = responses.gender || 'Unknown';
          demographicBreakdown.by_gender[gender] = (demographicBreakdown.by_gender[gender] || 0) + 1;

          // Role
          const role = responses.role_type || 'Unknown';
          demographicBreakdown.by_role[role] = (demographicBreakdown.by_role[role] || 0) + 1;

          // Age
          const age = responses.age;
          let ageGroup = 'Unknown';
          if (age) {
            const ageNum = typeof age === 'number' ? age : parseInt(String(age));
            if (!isNaN(ageNum)) {
              if (ageNum < 20) ageGroup = 'Under 20';
              else if (ageNum < 25) ageGroup = '20-24';
              else if (ageNum < 30) ageGroup = '25-29';
              else if (ageNum < 35) ageGroup = '30-34';
              else if (ageNum < 40) ageGroup = '35-39';
              else ageGroup = '40+';
            }
          }
          demographicBreakdown.by_age[ageGroup] = (demographicBreakdown.by_age[ageGroup] || 0) + 1;
        }
      });

      // Calculate test breakdown for this school
      const allTests = students.flatMap(s => s.testResults);
      const preTests = allTests.filter(t => t.testType === 'pre_test').length;
      const postTests = allTests.filter(t => t.testType === 'post_test').length;
      const averageTestsPerStudent = students.length > 0 ? allTests.length / students.length : 0;

      const testBreakdown = {
        totalTests: allTests.length,
        preTests,
        postTests,
        averageTestsPerStudent: Math.round(averageTestsPerStudent * 100) / 100
      };

      return {
        schoolName,
        totalStudents: students.length,
        studentsWithDemographics,
        studentsWithTests,
        studentsWithBoth,
        students,
        demographicBreakdown,
        testBreakdown
      };
    });

    // Calculate overall statistics
    const totalStudents = unifiedStudents.length;
    const studentsWithDemographics = unifiedStudents.filter(s => s.demographicData !== null).length;
    const studentsWithTests = unifiedStudents.filter(s => s.testResults.length > 0).length;
    const studentsWithBoth = unifiedStudents.filter(s => s.demographicData !== null && s.testResults.length > 0).length;

    // Calculate overall breakdowns
    const overallDemographicBreakdown = {
      by_country: {} as Record<string, number>,
      by_gender: {} as Record<string, number>,
      by_role: {} as Record<string, number>,
      by_age: {} as Record<string, number>
    };

    schools.forEach(school => {
      Object.entries(school.demographicBreakdown.by_country).forEach(([key, value]) => {
        overallDemographicBreakdown.by_country[key] = (overallDemographicBreakdown.by_country[key] || 0) + value;
      });
      Object.entries(school.demographicBreakdown.by_gender).forEach(([key, value]) => {
        overallDemographicBreakdown.by_gender[key] = (overallDemographicBreakdown.by_gender[key] || 0) + value;
      });
      Object.entries(school.demographicBreakdown.by_role).forEach(([key, value]) => {
        overallDemographicBreakdown.by_role[key] = (overallDemographicBreakdown.by_role[key] || 0) + value;
      });
      Object.entries(school.demographicBreakdown.by_age).forEach(([key, value]) => {
        overallDemographicBreakdown.by_age[key] = (overallDemographicBreakdown.by_age[key] || 0) + value;
      });
    });

    const overallTestBreakdown = {
      totalTests: schools.reduce((sum, school) => sum + school.testBreakdown.totalTests, 0),
      preTests: schools.reduce((sum, school) => sum + school.testBreakdown.preTests, 0),
      postTests: schools.reduce((sum, school) => sum + school.testBreakdown.postTests, 0),
      averageTestsPerStudent: totalStudents > 0 ? 
        schools.reduce((sum, school) => sum + school.testBreakdown.totalTests, 0) / totalStudents : 0
    };
    overallTestBreakdown.averageTestsPerStudent = Math.round(overallTestBreakdown.averageTestsPerStudent * 100) / 100;

    return {
      totalStudents,
      studentsWithDemographics,
      studentsWithTests,
      studentsWithBoth,
      totalSchools: schools.length,
      schools: schools.sort((a, b) => b.totalStudents - a.totalStudents), // Sort by student count
      overallDemographicBreakdown,
      overallTestBreakdown
    };

  } catch (error) {
    console.error('Error fetching unified analytics:', error);
    throw error;
  }
};
