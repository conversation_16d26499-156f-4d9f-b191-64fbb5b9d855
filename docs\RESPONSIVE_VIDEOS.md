# Responsive Video System

A comprehensive mobile-friendly video system for the LMS that ensures videos scale properly across all devices and screen sizes.

## 🎯 Overview

The responsive video system provides unified video handling across all contexts in the LMS:
- **Legacy JSON videos** (LessonContent component)
- **TipTap markdown videos** (embedded via data-youtube-video)
- **MarkdownPreview videos** (processed through markdown-to-HTML pipeline)

## 📱 Mobile Responsiveness

### Aspect Ratios by Screen Size
- **Desktop (>768px)**: 16:9 aspect ratio (standard widescreen)
- **Tablet (≤768px)**: 16:10 aspect ratio (slightly taller for better mobile viewing)
- **Mobile (≤480px)**: 16:11 aspect ratio (optimized for small screens)

### Key Features
- ✅ Proportional scaling on all devices
- ✅ Maintains aspect ratios without distortion
- ✅ Prevents overflow from container boundaries
- ✅ Fits within mobile viewport widths
- ✅ Touch-friendly (no hover effects on mobile)
- ✅ Dark mode compatible
- ✅ Accessibility focused (proper focus states)

## 🏗️ Architecture

### Core Files
1. **`src/styles/responsive-videos.css`** - Unified responsive video system
2. **`src/styles/tiptap.css`** - Enhanced TipTap video styling
3. **`src/styles/github-markdown.css`** - Markdown preview video styling
4. **`src/styles/unified-lesson-content.css`** - Legacy video compatibility
5. **`src/components/ui/markdown-preview.tsx`** - Automatic video wrapping

### CSS Variables
```css
:root {
  --video-border-radius: 0.75rem;
  --video-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --video-shadow-hover: 0 8px 24px rgba(0, 0, 0, 0.15);
  --video-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --video-aspect-ratio: 16 / 9;
  --video-aspect-ratio-mobile: 16 / 10;
  --video-max-width: 100%;
  --video-margin: 1.5rem 0;
}
```

## 🎨 CSS Classes

### Primary Classes
- `.responsive-video-container` - Main wrapper with styling and shadows
- `.responsive-video-wrapper` - Aspect ratio container
- `.responsive-video-container.loading` - Loading state with shimmer effect
- `.responsive-video-container.error` - Error state styling

### Platform-Specific Classes
- `.youtube-video-container` - YouTube-specific styling
- `.vimeo-video-container` - Vimeo-specific styling
- `.generic-video-container` - Generic video styling

## 🔧 Implementation Details

### Automatic Video Wrapping
The MarkdownPreview component automatically wraps video elements:

```typescript
onEnhanceVideos: (container) => {
  const videoElements = container.querySelectorAll(
    'iframe[src*="youtube.com"], iframe[src*="youtu.be"], iframe[src*="vimeo.com"], video'
  );
  
  videoElements.forEach(video => {
    // Create responsive wrapper
    const wrapper = document.createElement('div');
    wrapper.className = 'responsive-video-container';
    
    const innerWrapper = document.createElement('div');
    innerWrapper.className = 'responsive-video-wrapper';
    
    // Wrap video element
    video.parentNode?.insertBefore(wrapper, video);
    wrapper.appendChild(innerWrapper);
    innerWrapper.appendChild(video);
  });
}
```

### TipTap Integration
Videos in TipTap editor use enhanced styling:

```css
.ProseMirror [data-youtube-video] {
  aspect-ratio: var(--video-aspect-ratio, 16 / 9);
  border-radius: var(--video-border-radius, 0.75rem);
  box-shadow: var(--video-shadow, 0 4px 12px rgba(0, 0, 0, 0.1));
  /* ... */
}
```

## 📱 Mobile Optimizations

### Breakpoint Strategy
```css
/* Tablet and below */
@media (max-width: 768px) {
  .responsive-video-wrapper {
    aspect-ratio: var(--video-aspect-ratio-mobile, 16 / 10);
  }
  
  .responsive-video-container:hover {
    transform: none; /* Disable hover effects */
  }
}

/* Mobile phones */
@media (max-width: 480px) {
  .responsive-video-wrapper {
    aspect-ratio: 16 / 11; /* Taller for small screens */
    border-radius: 0.375rem; /* Smaller border radius */
  }
}
```

### Touch Optimization
- Hover effects disabled on mobile devices
- Larger touch targets for controls
- Optimized spacing for finger navigation

## 🌙 Dark Mode Support

All video containers include dark mode variants:

```css
.dark .responsive-video-container {
  background: linear-gradient(135deg, hsl(var(--muted)/0.2) 0%, hsl(var(--muted)/0.1) 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}
```

## 🎬 Supported Platforms

- ✅ **YouTube** (`youtube.com`, `youtu.be`)
- ✅ **Vimeo** (`vimeo.com`, `player.vimeo.com`)
- ✅ **Direct video files** (MP4, WebM, etc.)
- ✅ **Data URLs** (uploaded videos)

## 🧪 Testing

### Automated Testing
Run the test script to verify implementation:
```bash
node scripts/test-responsive-videos.js
```

### Manual Testing
1. Open `scripts/responsive-video-test.html` in browser
2. Resize window to test different breakpoints
3. Verify aspect ratios maintain properly
4. Check hover effects (desktop only)
5. Test in both light and dark modes

### Test Checklist
- [ ] Videos scale proportionally on mobile
- [ ] Aspect ratios maintained at all screen sizes
- [ ] No horizontal scrolling on mobile
- [ ] Videos fit within viewport
- [ ] Hover effects work on desktop only
- [ ] Dark mode styling applied correctly
- [ ] Loading states display properly
- [ ] Error states handle gracefully

## 🚀 Usage Examples

### Basic Responsive Video
```html
<div class="responsive-video-container">
  <div class="responsive-video-wrapper">
    <iframe src="https://www.youtube.com/embed/VIDEO_ID" frameborder="0" allowfullscreen></iframe>
  </div>
</div>
```

### TipTap Markdown Video
```html
<div data-youtube-video>
  <iframe src="https://www.youtube.com/embed/VIDEO_ID" frameborder="0" allowfullscreen></iframe>
</div>
```

### Automatic Wrapping
Videos in markdown content are automatically wrapped by the MarkdownPreview component.

## 🔄 Migration Guide

### From Legacy System
1. Existing videos continue to work without changes
2. New responsive features applied automatically
3. Mobile optimizations active immediately

### CSS Import
The responsive video CSS is automatically imported in `main.tsx`:
```typescript
import './styles/responsive-videos.css';
```

## 🎯 Performance

### Optimizations
- CSS variables for consistent theming
- Hardware-accelerated transitions
- Lazy loading support for iframes
- Efficient aspect ratio calculations using CSS `aspect-ratio`

### Loading States
- Shimmer animation during video load
- Graceful error handling with user-friendly messages
- Timeout fallbacks for slow connections

## 🔧 Customization

### Aspect Ratios
Modify CSS variables to change aspect ratios:
```css
:root {
  --video-aspect-ratio: 4 / 3; /* Square-ish videos */
  --video-aspect-ratio-mobile: 1 / 1; /* Square on mobile */
}
```

### Styling
Override CSS variables for custom styling:
```css
:root {
  --video-border-radius: 1rem; /* More rounded corners */
  --video-shadow: 0 8px 24px rgba(0, 0, 0, 0.2); /* Stronger shadow */
}
```

## 📊 Browser Support

- ✅ Modern browsers with CSS `aspect-ratio` support
- ✅ Fallback for older browsers using padding-bottom technique
- ✅ Mobile Safari optimizations
- ✅ Chrome, Firefox, Edge, Safari

## 🎉 Benefits

1. **Consistent Experience**: Unified video behavior across all contexts
2. **Mobile-First**: Optimized for mobile devices and touch interfaces
3. **Performance**: Efficient CSS-based responsive design
4. **Accessibility**: Proper focus states and keyboard navigation
5. **Maintainable**: Single source of truth for video styling
6. **Future-Proof**: Uses modern CSS features with fallbacks
