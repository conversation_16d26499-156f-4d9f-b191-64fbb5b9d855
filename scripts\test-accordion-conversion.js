#!/usr/bin/env node

/**
 * Test script to verify accordion/dropdown conversion functionality
 * This script tests the markdown to HTML conversion for accordion syntax
 */

const fs = require('fs');
const path = require('path');

// Mock the content converter for testing
function processDropdownContent(content) {
  if (!content) return '';
  
  let html = content;
  
  // Headers
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
  html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
  
  // Bold
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  
  // Italic
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
  
  // Links
  html = html.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2">$1</a>');
  
  // Code blocks
  html = html.replace(/```([\\s\\S]*?)```/gm, '<pre><code>$1</code></pre>');
  
  // Inline code
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>');
  
  // Lists
  html = html.replace(/^\\s*[\\*\\-]\\s(.+)$/gm, '<li>$1</li>');
  html = html.replace(/(<li>.*<\\/li>\\n)+/g, '<ul>$&</ul>');

  // Paragraphs for remaining text
  html = html.split('\\n\\n').map(para => {
    // Skip if it's already a block element
    if (para.trim().startsWith('<') &&
        !para.trim().startsWith('<span') &&
        !para.trim().startsWith('<strong') &&
        !para.trim().startsWith('<em')) return para;
    return `<p>${para}</p>`;
  }).join('\\n');
  
  return html;
}

function testAccordionConversion(content) {
  console.log('🧪 Testing Accordion Conversion');
  console.log('================================');
  
  // Process dropdown/accordion syntax: (dropdown)<content>
  const processedContent = content.replace(/\\(dropdown\\)<([\\s\\S]*?)>/gm, (match, content) => {
    const processedContent = processDropdownContent(content.trim());
    // Extract first line as summary, rest as content
    const lines = content.trim().split('\\n');
    let summary = 'Click to expand';
    let dropdownContent = content.trim();

    // If first line is a header, use it as summary
    const headerMatch = lines[0]?.match(/^#+\\s*(.+)$/);
    if (headerMatch) {
      summary = headerMatch[1];
      dropdownContent = lines.slice(1).join('\\n').trim();
    }
    
    const processedDropdownContent = processDropdownContent(dropdownContent);
    
    return `<details class="lesson-accordion">
      <summary class="lesson-accordion-trigger">${summary}</summary>
      <div class="lesson-accordion-content">${processedDropdownContent}</div>
    </details>`;
  });
  
  return processedContent;
}

// Test cases
const testCases = [
  {
    name: 'Simple Accordion',
    input: `# Test Content

(dropdown)<
### More Information
This is hidden content that will be revealed.
>

Regular content continues.`,
    expected: 'Should convert to details/summary HTML'
  },
  {
    name: 'Multiple Accordions',
    input: `# Multiple Accordions

(dropdown)<
### First Section
Content for first accordion.
>

(dropdown)<
### Second Section
Content for second accordion.
>`,
    expected: 'Should convert multiple accordions'
  },
  {
    name: 'Accordion with Rich Content',
    input: `# Rich Content Test

(dropdown)<
### Technical Details
This has **bold** and *italic* text.

- List item 1
- List item 2

```javascript
console.log("Code block");
```
>`,
    expected: 'Should handle rich markdown content'
  }
];

console.log('🚀 Starting Accordion Conversion Tests\n');

testCases.forEach((testCase, index) => {
  console.log(`📝 Test ${index + 1}: ${testCase.name}`);
  console.log('Input:');
  console.log(testCase.input);
  console.log('\nOutput:');
  const result = testAccordionConversion(testCase.input);
  console.log(result);
  console.log('\nExpected:', testCase.expected);
  console.log('\n' + '='.repeat(50) + '\n');
});

console.log('✅ Accordion conversion tests completed!');
console.log('\n💡 To test in browser, visit: http://localhost:8081/accordion-test');
