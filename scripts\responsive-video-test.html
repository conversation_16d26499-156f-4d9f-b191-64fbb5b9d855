
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Responsive Video Test</title>
    <link rel="stylesheet" href="../src/styles/responsive-videos.css">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 40px 0; padding: 20px; border: 1px solid #ccc; }
        .screen-size { position: fixed; top: 10px; right: 10px; background: #333; color: white; padding: 5px 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="screen-size" id="screenSize"></div>
    
    <h1>Responsive Video Test</h1>
    
    <div class="test-section">
        <h2>1. Responsive Video Container</h2>
        <div class="responsive-video-container">
            <div class="responsive-video-wrapper">
                <iframe src="https://www.youtube.com/embed/dQw4w9WgXcQ" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>2. TipTap Style Video</h2>
        <div data-youtube-video>
            <iframe src="https://www.youtube.com/embed/oHg5SJYRHA0" frameborder="0" allowfullscreen></iframe>
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. Markdown Preview Style Video</h2>
        <div class="markdown-preview">
            <iframe src="https://www.youtube.com/embed/ScMzIvxBSi4" frameborder="0" allowfullscreen></iframe>
        </div>
    </div>
    
    <div class="test-section">
        <h2>4. Vimeo Video</h2>
        <div class="responsive-video-container">
            <div class="responsive-video-wrapper">
                <iframe src="https://player.vimeo.com/video/76979871" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
    
    <script>
        function updateScreenSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            document.getElementById('screenSize').textContent = width + 'x' + height;
        }
        
        updateScreenSize();
        window.addEventListener('resize', updateScreenSize);
    </script>
</body>
</html>
