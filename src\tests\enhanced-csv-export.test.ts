/**
 * Enhanced CSV Export Test Suite
 * 
 * This test verifies the improved CSV export functionality including:
 * - Data quality and completeness
 * - Proper formatting and encoding
 * - Clear column headers and organization
 * - Test data presentation improvements
 */

import { 
  UnifiedStudentData, 
  SchoolUnifiedData, 
  EnhancedUnifiedExportData 
} from '@/types/unified-analytics';
import { 
  convertUnifiedDataToEnhancedExportFormat,
  convertEnhancedUnifiedExportDataToCSV 
} from '@/lib/unifiedCsvExport';

// Comprehensive mock data for testing
const mockStudentWithFullData: UnifiedStudentData = {
  student: {
    id: 'student-001',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    fullName: '<PERSON>'
  },
  demographicData: {
    responses: {
      consent: 'Agree',
      country: 'Ghana',
      gender: 'Male',
      age: 22,
      formal_training: 'Yes',
      role_type: 'Student',
      student_level: 'Undergraduate',
      university: 'University of Ghana',
      undergraduate_program: 'BSc Diagnostic Radiography',
      undergraduate_year: '3rd Year',
      postgraduate_program: '',
      practitioner_work: '',
      workplace: '',
      location: '',
      experience_years: ''
    },
    completedAt: '2024-01-15T10:30:00Z',
    school: 'University of Ghana'
  },
  testResults: [
    {
      id: 'test-001',
      testId: 'pre-test-1',
      testTitle: 'Module 1 Pre-Assessment',
      testType: 'pre_test',
      moduleId: 'module-1',
      moduleTitle: 'Introduction to IV Cannulation',
      courseId: 'course-1',
      courseTitle: 'Medical Imaging Fundamentals',
      submittedAt: '2024-01-20T14:15:00Z',
      responseCount: 10
    },
    {
      id: 'test-002',
      testId: 'post-test-1',
      testTitle: 'Module 1 Post-Assessment',
      testType: 'post_test',
      moduleId: 'module-1',
      moduleTitle: 'Introduction to IV Cannulation',
      courseId: 'course-1',
      courseTitle: 'Medical Imaging Fundamentals',
      submittedAt: '2024-01-25T16:45:00Z',
      responseCount: 10
    }
  ],
  totalTestsCompleted: 2
};

const mockStudentWithPartialData: UnifiedStudentData = {
  student: {
    id: 'student-002',
    email: '<EMAIL>',
    firstName: 'Jane',
    lastName: 'Smith',
    fullName: 'Jane Smith'
  },
  demographicData: {
    responses: {
      consent: 'Agree',
      country: 'Ghana',
      gender: 'Female',
      age: 21,
      formal_training: 'No',
      role_type: 'Student',
      student_level: 'Undergraduate',
      university: 'KNUST',
      undergraduate_program: 'BSc Nuclear Medicine',
      undergraduate_year: '2nd Year'
    },
    completedAt: '2024-01-10T09:00:00Z',
    school: 'KNUST'
  },
  testResults: [
    {
      id: 'test-003',
      testId: 'pre-test-1',
      testTitle: 'Module 1 Pre-Assessment',
      testType: 'pre_test',
      moduleId: 'module-1',
      moduleTitle: 'Introduction to IV Cannulation',
      courseId: 'course-1',
      courseTitle: 'Medical Imaging Fundamentals',
      submittedAt: '2024-01-18T11:30:00Z',
      responseCount: 8
    }
  ],
  totalTestsCompleted: 1
};

const mockStudentWithNoTests: UnifiedStudentData = {
  student: {
    id: 'student-003',
    email: '<EMAIL>',
    firstName: 'Alex',
    lastName: 'Johnson',
    fullName: 'Alex Johnson'
  },
  demographicData: {
    responses: {
      consent: 'Agree',
      country: 'Ghana',
      gender: 'Male',
      age: 24,
      formal_training: 'Yes',
      role_type: 'Student',
      student_level: 'Postgraduate',
      university: 'University of Cape Coast',
      postgraduate_program: 'MSc Medical Imaging'
    },
    completedAt: '2024-01-12T15:20:00Z',
    school: 'University of Cape Coast'
  },
  testResults: [],
  totalTestsCompleted: 0
};

const mockSchoolData: SchoolUnifiedData = {
  schoolName: 'University of Ghana',
  totalStudents: 3,
  studentsWithDemographics: 3,
  studentsWithTests: 2,
  studentsWithBoth: 2,
  students: [mockStudentWithFullData, mockStudentWithPartialData, mockStudentWithNoTests],
  demographicBreakdown: {
    by_country: { 'Ghana': 3 },
    by_gender: { 'Male': 2, 'Female': 1 },
    by_role: { 'Student': 3 },
    by_age: { '20-24': 3 }
  },
  testBreakdown: {
    totalTests: 3,
    preTests: 2,
    postTests: 1,
    averageTestsPerStudent: 1.0
  }
};

/**
 * Test enhanced CSV data structure and formatting
 */
export function testEnhancedCSVStructure(): boolean {
  console.log('Testing enhanced CSV data structure...');
  
  try {
    const exportData = convertUnifiedDataToEnhancedExportFormat(
      mockSchoolData.students, 
      mockSchoolData.schoolName
    );

    // Should have 4 rows: 2 for student with 2 tests, 1 for student with 1 test, 1 for student with no tests
    if (exportData.length !== 4) {
      throw new Error(`Expected 4 export rows, got ${exportData.length}`);
    }

    // Test student with full data (should have 2 rows)
    const fullDataRows = exportData.filter(row => row.studentId === 'student-001');
    if (fullDataRows.length !== 2) {
      throw new Error(`Expected 2 rows for student with full data, got ${fullDataRows.length}`);
    }

    // Test student with partial data (should have 1 row)
    const partialDataRows = exportData.filter(row => row.studentId === 'student-002');
    if (partialDataRows.length !== 1) {
      throw new Error(`Expected 1 row for student with partial data, got ${partialDataRows.length}`);
    }

    // Test student with no tests (should have 1 row)
    const noTestRows = exportData.filter(row => row.studentId === 'student-003');
    if (noTestRows.length !== 1) {
      throw new Error(`Expected 1 row for student with no tests, got ${noTestRows.length}`);
    }

    // Verify test type labels
    const preTestRow = fullDataRows.find(row => row.testType === 'pre_test');
    if (!preTestRow || preTestRow.testTypeLabel !== 'Pre-Test') {
      throw new Error('Pre-test label not correctly formatted');
    }

    const postTestRow = fullDataRows.find(row => row.testType === 'post_test');
    if (!postTestRow || postTestRow.testTypeLabel !== 'Post-Test') {
      throw new Error('Post-test label not correctly formatted');
    }

    // Verify completion order
    if (preTestRow.testCompletionOrder !== '1' || postTestRow.testCompletionOrder !== '2') {
      throw new Error('Test completion order not correctly calculated');
    }

    console.log('✅ Enhanced CSV data structure test passed');
    return true;
  } catch (error) {
    console.error('❌ Enhanced CSV data structure test failed:', error);
    return false;
  }
}

/**
 * Test CSV formatting and encoding
 */
export function testCSVFormattingAndEncoding(): boolean {
  console.log('Testing CSV formatting and encoding...');
  
  try {
    const exportData = convertUnifiedDataToEnhancedExportFormat(
      mockSchoolData.students, 
      mockSchoolData.schoolName
    );
    
    const csvContent = convertEnhancedUnifiedExportDataToCSV(exportData);

    // Check for UTF-8 BOM
    if (!csvContent.startsWith('\uFEFF')) {
      throw new Error('CSV content missing UTF-8 BOM');
    }

    // Check for proper header structure
    const lines = csvContent.split('\n');
    const headerLine = lines[0].substring(1); // Remove BOM
    
    const expectedHeaders = [
      'School/University',
      'Student ID',
      'Student Name',
      'Email Address',
      'Test Title',
      'Test Type (Label)',
      'Test Submission Date'
    ];

    for (const header of expectedHeaders) {
      if (!headerLine.includes(header)) {
        throw new Error(`Missing expected header: ${header}`);
      }
    }

    // Check for proper data rows (should have header + 4 data rows)
    if (lines.length < 5) {
      throw new Error(`Expected at least 5 lines (header + 4 data rows), got ${lines.length}`);
    }

    // Verify date formatting (should be YYYY-MM-DD HH:MM:SS)
    const dataLine = lines[1]; // First data row
    const dateRegex = /\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/;
    if (!dateRegex.test(dataLine)) {
      console.warn('Date formatting may not be consistent in CSV');
    }

    console.log('✅ CSV formatting and encoding test passed');
    return true;
  } catch (error) {
    console.error('❌ CSV formatting and encoding test failed:', error);
    return false;
  }
}

/**
 * Test missing data handling
 */
export function testMissingDataHandling(): boolean {
  console.log('Testing missing data handling...');
  
  try {
    // Create student with minimal data
    const minimalStudent: UnifiedStudentData = {
      student: {
        id: 'minimal-001',
        email: '',
        fullName: ''
      },
      demographicData: null,
      testResults: [],
      totalTestsCompleted: 0
    };

    const exportData = convertUnifiedDataToEnhancedExportFormat([minimalStudent], 'Test School');
    
    if (exportData.length !== 1) {
      throw new Error(`Expected 1 export row for minimal student, got ${exportData.length}`);
    }

    const row = exportData[0];
    
    // Check that missing data is handled gracefully
    if (row.email !== 'Not Provided') {
      throw new Error(`Expected 'Not Provided' for missing email, got '${row.email}'`);
    }

    if (row.demographicCompletionStatus !== 'Not Completed') {
      throw new Error(`Expected 'Not Completed' for missing demographics, got '${row.demographicCompletionStatus}'`);
    }

    if (row.testCompletionStatus !== 'No Tests Completed') {
      throw new Error(`Expected 'No Tests Completed' for no tests, got '${row.testCompletionStatus}'`);
    }

    console.log('✅ Missing data handling test passed');
    return true;
  } catch (error) {
    console.error('❌ Missing data handling test failed:', error);
    return false;
  }
}

/**
 * Run all enhanced CSV export tests
 */
export function runEnhancedCSVExportTests(): boolean {
  console.log('🧪 Running Enhanced CSV Export Tests...\n');
  
  const tests = [
    testEnhancedCSVStructure,
    testCSVFormattingAndEncoding,
    testMissingDataHandling
  ];
  
  let passedTests = 0;
  const totalTests = tests.length;
  
  for (const test of tests) {
    try {
      if (test()) {
        passedTests++;
      }
    } catch (error) {
      console.error('Test execution error:', error);
    }
    console.log(''); // Add spacing between tests
  }
  
  console.log(`📊 Enhanced CSV Export Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All enhanced CSV export tests passed!');
    return true;
  } else {
    console.log('⚠️  Some tests failed. Please review the implementation.');
    return false;
  }
}

// Export test data for manual verification
export {
  mockStudentWithFullData,
  mockStudentWithPartialData,
  mockStudentWithNoTests,
  mockSchoolData
};
