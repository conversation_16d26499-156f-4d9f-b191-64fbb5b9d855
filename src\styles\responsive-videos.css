/* Unified Responsive Video System */
/* Comprehensive mobile-friendly video containers for all contexts */

:root {
  --video-border-radius: 0.75rem;
  --video-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  --video-shadow-hover: 0 8px 24px rgba(0, 0, 0, 0.15);
  --video-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --video-aspect-ratio: 16 / 9;
  --video-aspect-ratio-mobile: 16 / 10;
  --video-max-width: 100%;
  --video-margin: 1.5rem 0;
}

/* Base responsive video container */
.responsive-video-container {
  position: relative;
  width: 100%;
  max-width: var(--video-max-width);
  margin: var(--video-margin);
  border-radius: var(--video-border-radius);
  overflow: hidden;
  background: linear-gradient(135deg, hsl(var(--muted)/0.1) 0%, hsl(var(--muted)/0.05) 100%);
  box-shadow: var(--video-shadow);
  transition: var(--video-transition);
}

.responsive-video-container:hover {
  box-shadow: var(--video-shadow-hover);
  transform: translateY(-2px);
}

/* Aspect ratio wrapper */
.responsive-video-wrapper {
  position: relative;
  width: 100%;
  aspect-ratio: var(--video-aspect-ratio);
  overflow: hidden;
}

/* Video elements */
.responsive-video-wrapper iframe,
.responsive-video-wrapper video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: var(--video-border-radius);
  object-fit: cover;
  transition: var(--video-transition);
}

/* Focus states for accessibility */
.responsive-video-wrapper iframe:focus,
.responsive-video-wrapper video:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Loading state */
.responsive-video-container.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, hsl(var(--muted)/0.2) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: video-shimmer 2s infinite;
  z-index: 1;
  pointer-events: none;
}

@keyframes video-shimmer {
  0% { background-position: -200% -200%; }
  100% { background-position: 200% 200%; }
}

/* Error state */
.responsive-video-container.error {
  background: linear-gradient(135deg, hsl(var(--destructive)/0.1) 0%, hsl(var(--destructive)/0.05) 100%);
  border: 1px solid hsl(var(--destructive)/0.2);
  color: hsl(var(--destructive));
}

.responsive-video-container.error .video-error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  text-align: center;
  padding: 1rem;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .responsive-video-container {
    margin: 1rem 0;
    border-radius: 0.5rem;
  }
  
  .responsive-video-wrapper {
    aspect-ratio: var(--video-aspect-ratio-mobile);
  }
  
  .responsive-video-wrapper iframe,
  .responsive-video-wrapper video {
    border-radius: 0.5rem;
  }
  
  .responsive-video-container:hover {
    transform: none; /* Disable hover effects on mobile */
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .responsive-video-container {
    margin: 0.75rem 0;
    border-radius: 0.375rem;
  }
  
  .responsive-video-wrapper {
    aspect-ratio: 16 / 11; /* Slightly taller for very small screens */
  }
  
  .responsive-video-wrapper iframe,
  .responsive-video-wrapper video {
    border-radius: 0.375rem;
  }
}

/* Large screens */
@media (min-width: 1200px) {
  .responsive-video-container {
    max-width: 900px; /* Limit max width on very large screens */
    margin: 2rem auto; /* Center on large screens */
  }
  
  .responsive-video-container:hover {
    transform: translateY(-4px);
  }
}

/* Dark mode adjustments */
.dark .responsive-video-container {
  background: linear-gradient(135deg, hsl(var(--muted)/0.2) 0%, hsl(var(--muted)/0.1) 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .responsive-video-container:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

/* Platform-specific video containers */
.youtube-video-container,
.vimeo-video-container,
.generic-video-container {
  @apply responsive-video-container;
}

/* Ensure videos don't overflow their containers */
.responsive-video-container * {
  max-width: 100%;
  height: auto;
}

/* Markdown preview video integration */
.markdown-preview iframe[src*="youtube.com"],
.markdown-preview iframe[src*="youtu.be"],
.markdown-preview iframe[src*="vimeo.com"],
.markdown-preview video {
  width: 100% !important;
  height: auto !important;
  aspect-ratio: 16 / 9;
  border-radius: var(--video-border-radius);
  box-shadow: var(--video-shadow);
  transition: var(--video-transition);
  margin: var(--video-margin);
}

.markdown-preview iframe[src*="youtube.com"]:hover,
.markdown-preview iframe[src*="youtu.be"]:hover,
.markdown-preview iframe[src*="vimeo.com"]:hover,
.markdown-preview video:hover {
  box-shadow: var(--video-shadow-hover);
  transform: translateY(-2px);
}

/* TipTap editor video enhancements */
.ProseMirror [data-youtube-video] {
  position: relative;
  width: 100%;
  aspect-ratio: var(--video-aspect-ratio);
  overflow: hidden;
  margin: var(--video-margin);
  border-radius: var(--video-border-radius);
  box-shadow: var(--video-shadow);
  background: linear-gradient(135deg, hsl(var(--muted)/0.1) 0%, hsl(var(--muted)/0.05) 100%);
  transition: var(--video-transition);
}

.ProseMirror [data-youtube-video]:hover {
  box-shadow: var(--video-shadow-hover);
  transform: translateY(-2px);
}

.ProseMirror [data-youtube-video] iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: var(--video-border-radius);
}

/* Legacy video container compatibility */
.enhanced-video-container {
  @apply responsive-video-container;
}

.enhanced-video-container .video-wrapper {
  @apply responsive-video-wrapper;
}

/* Mobile-specific TipTap adjustments */
@media (max-width: 768px) {
  .ProseMirror [data-youtube-video] {
    aspect-ratio: var(--video-aspect-ratio-mobile);
    margin: 1rem 0;
    border-radius: 0.5rem;
  }

  .ProseMirror [data-youtube-video] iframe {
    border-radius: 0.5rem;
  }

  .ProseMirror [data-youtube-video]:hover {
    transform: none;
  }

  .markdown-preview iframe[src*="youtube.com"]:hover,
  .markdown-preview iframe[src*="youtu.be"]:hover,
  .markdown-preview iframe[src*="vimeo.com"]:hover,
  .markdown-preview video:hover {
    transform: none;
  }
}

@media (max-width: 480px) {
  .ProseMirror [data-youtube-video] {
    aspect-ratio: 16 / 11;
    margin: 0.75rem 0;
    border-radius: 0.375rem;
  }

  .ProseMirror [data-youtube-video] iframe {
    border-radius: 0.375rem;
  }

  .markdown-preview iframe[src*="youtube.com"],
  .markdown-preview iframe[src*="youtu.be"],
  .markdown-preview iframe[src*="vimeo.com"],
  .markdown-preview video {
    aspect-ratio: 16 / 11;
    margin: 0.75rem 0;
    border-radius: 0.375rem;
  }
}

/* Dark mode for all video contexts */
.dark .ProseMirror [data-youtube-video] {
  background: linear-gradient(135deg, hsl(var(--muted)/0.2) 0%, hsl(var(--muted)/0.1) 100%);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .ProseMirror [data-youtube-video]:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

.dark .markdown-preview iframe[src*="youtube.com"],
.dark .markdown-preview iframe[src*="youtu.be"],
.dark .markdown-preview iframe[src*="vimeo.com"],
.dark .markdown-preview video {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark .markdown-preview iframe[src*="youtube.com"]:hover,
.dark .markdown-preview iframe[src*="youtu.be"]:hover,
.dark .markdown-preview iframe[src*="vimeo.com"]:hover,
.dark .markdown-preview video:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

/* Print styles */
@media print {
  .responsive-video-container,
  .ProseMirror [data-youtube-video],
  .markdown-preview iframe[src*="youtube.com"],
  .markdown-preview iframe[src*="youtu.be"],
  .markdown-preview iframe[src*="vimeo.com"],
  .markdown-preview video {
    display: none;
  }

  .responsive-video-container::after,
  .ProseMirror [data-youtube-video]::after,
  .markdown-preview iframe[src*="youtube.com"]::after,
  .markdown-preview iframe[src*="youtu.be"]::after,
  .markdown-preview iframe[src*="vimeo.com"]::after,
  .markdown-preview video::after {
    content: "Video content available in digital version";
    display: block;
    text-align: center;
    padding: 2rem;
    background: hsl(var(--muted)/0.1);
    border: 1px solid hsl(var(--border));
    border-radius: 0.5rem;
    font-style: italic;
    color: hsl(var(--muted-foreground));
  }
}
