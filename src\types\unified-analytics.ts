import { DemographicResponse, UserDemographicResponse } from './demographic';
import { TestSubmissionSummary, StudentProfile } from './test-analytics';

/**
 * Combined student data including demographics and test results
 */
export interface UnifiedStudentData {
  student: StudentProfile;
  demographicData: {
    responses: DemographicResponse;
    completedAt: string;
    school: string;
  } | null;
  testResults: TestSubmissionSummary[];
  totalTestsCompleted: number;
}

/**
 * School-specific unified analytics data
 */
export interface SchoolUnifiedData {
  schoolName: string;
  totalStudents: number;
  studentsWithDemographics: number;
  studentsWithTests: number;
  studentsWithBoth: number;
  students: UnifiedStudentData[];
  demographicBreakdown: {
    by_country: Record<string, number>;
    by_gender: Record<string, number>;
    by_role: Record<string, number>;
    by_age: Record<string, number>;
  };
  testBreakdown: {
    totalTests: number;
    preTests: number;
    postTests: number;
    averageTestsPerStudent: number;
  };
}

/**
 * Overall unified analytics overview
 */
export interface UnifiedAnalyticsOverview {
  totalStudents: number;
  studentsWithDemographics: number;
  studentsWithTests: number;
  studentsWithBoth: number;
  totalSchools: number;
  schools: SchoolUnifiedData[];
  overallDemographicBreakdown: {
    by_country: Record<string, number>;
    by_gender: Record<string, number>;
    by_role: Record<string, number>;
    by_age: Record<string, number>;
  };
  overallTestBreakdown: {
    totalTests: number;
    preTests: number;
    postTests: number;
    averageTestsPerStudent: number;
  };
}

/**
 * Export data structure for CSV downloads (legacy format)
 */
export interface UnifiedExportData {
  schoolName: string;
  studentId: string;
  studentName: string;
  email: string;
  // Demographic fields
  demographicCompletedAt?: string;
  consent?: string;
  country?: string;
  gender?: string;
  age?: number;
  formal_training?: string;
  role_type?: string;
  student_level?: string;
  university?: string;
  undergraduate_program?: string;
  undergraduate_year?: string;
  postgraduate_program?: string;
  practitioner_work?: string;
  workplace?: string;
  location?: string;
  experience_years?: string;
  // Test results summary
  totalTestsCompleted: number;
  preTestsCompleted: number;
  postTestsCompleted: number;
  latestTestDate?: string;
  testDetails: string; // JSON string of test submissions
}

/**
 * Enhanced export data structure for detailed CSV downloads with individual test rows
 */
export interface EnhancedUnifiedExportData {
  // Student Information
  schoolName: string;
  studentId: string;
  studentName: string;
  email: string;

  // Demographic Information
  demographicCompletedAt: string;
  demographicCompletionStatus: string;
  consent: string;
  country: string;
  gender: string;
  age: string;
  formalTraining: string;
  roleType: string;
  studentLevel: string;
  university: string;
  undergraduateProgram: string;
  undergraduateYear: string;
  postgraduateProgram: string;
  practitionerWork: string;
  workplace: string;
  location: string;
  experienceYears: string;

  // Test Summary Information
  totalTestsCompleted: number;
  preTestsCompleted: number;
  postTestsCompleted: number;
  earliestTestDate: string;
  latestTestDate: string;
  testCompletionStatus: string;

  // Individual Test Information (one row per test)
  testTitle: string;
  testType: string;
  testTypeLabel: string;
  moduleTitle: string;
  courseTitle: string;
  testSubmittedAt: string;
  testResponseCount: string;
  testCompletionOrder: string;
}
