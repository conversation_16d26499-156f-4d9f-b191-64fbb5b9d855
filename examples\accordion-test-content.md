# Accordion Test Content

This is a test document to verify that the accordion functionality works properly in lesson content.

## Regular Content

This is regular paragraph content that should display normally.

## Accordion Examples

Here are some examples of accordion/collapsible sections:

(dropdown)<
### Additional Information

This content is hidden by default and will be revealed when the user clicks on the accordion trigger.

- This is a list item inside the accordion
- Another list item
- And one more

**Bold text** and *italic text* work inside accordions too!
>

## More Content

Regular content continues here.

(dropdown)<
### Technical Details

This is another accordion section with different content.

```javascript
// Code blocks work inside accordions
function example() {
  console.log("Hello from inside an accordion!");
  return "This is working!";
}
```

You can include any markdown content inside accordion sections:

1. Numbered lists
2. Code blocks
3. Images (if you have URLs)
4. Links: [Example Link](https://example.com)
5. Tables

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Data 1   | Data 2   | Data 3   |
| More     | Data     | Here     |
>

## Final Section

(dropdown)<
### FAQ Section

**Q: How do accordions work?**
A: Accordions allow you to hide content by default and reveal it when clicked.

**Q: Can I nest content inside accordions?**
A: Yes! You can include any markdown content inside accordion sections.

**Q: Are accordions mobile-friendly?**
A: Yes, the accordion styling is responsive and works well on mobile devices.
>

This concludes the accordion test content. All accordion sections should be closed by default when the page loads.
