/**
 * Unified Analytics System Test
 * 
 * This test verifies the basic functionality of the unified analytics system
 * by checking data structure integrity and export functionality.
 */

import { 
  UnifiedStudentData, 
  SchoolUnifiedData, 
  UnifiedAnalyticsOverview,
  UnifiedExportData 
} from '@/types/unified-analytics';
import { 
  convertUnifiedDataToExportFormat,
  convertUnifiedExportDataToCSV 
} from '@/lib/unifiedCsvExport';

// Mock data for testing
const mockStudentData: UnifiedStudentData = {
  student: {
    id: 'test-user-1',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    fullName: '<PERSON>'
  },
  demographicData: {
    responses: {
      consent: 'Agree',
      country: 'Ghana',
      gender: 'Male',
      age: 22,
      formal_training: 'Yes',
      role_type: 'Student',
      student_level: 'Undergraduate',
      university: 'University of Ghana'
    },
    completedAt: '2024-01-15T10:00:00Z',
    school: 'University of Ghana'
  },
  testResults: [
    {
      id: 'test-response-1',
      testId: 'test-1',
      testTitle: 'Module 1 Pre-Test',
      testType: 'pre_test',
      moduleId: 'module-1',
      moduleTitle: 'Introduction to Healthcare',
      courseId: 'course-1',
      courseTitle: 'Healthcare Fundamentals',
      submittedAt: '2024-01-20T14:30:00Z',
      responseCount: 5
    },
    {
      id: 'test-response-2',
      testId: 'test-2',
      testTitle: 'Module 1 Post-Test',
      testType: 'post_test',
      moduleId: 'module-1',
      moduleTitle: 'Introduction to Healthcare',
      courseId: 'course-1',
      courseTitle: 'Healthcare Fundamentals',
      submittedAt: '2024-01-25T16:45:00Z',
      responseCount: 5
    }
  ],
  totalTestsCompleted: 2
};

const mockSchoolData: SchoolUnifiedData = {
  schoolName: 'University of Ghana',
  totalStudents: 1,
  studentsWithDemographics: 1,
  studentsWithTests: 1,
  studentsWithBoth: 1,
  students: [mockStudentData],
  demographicBreakdown: {
    by_country: { 'Ghana': 1 },
    by_gender: { 'Male': 1 },
    by_role: { 'Student': 1 },
    by_age: { '20-24': 1 }
  },
  testBreakdown: {
    totalTests: 2,
    preTests: 1,
    postTests: 1,
    averageTestsPerStudent: 2.0
  }
};

/**
 * Test unified analytics data structure integrity
 */
export function testDataStructureIntegrity(): boolean {
  console.log('Testing unified analytics data structure integrity...');
  
  try {
    // Test student data structure
    if (!mockStudentData.student.id || !mockStudentData.student.email) {
      throw new Error('Student data missing required fields');
    }
    
    // Test demographic data structure
    if (!mockStudentData.demographicData?.responses.university) {
      throw new Error('Demographic data missing university field');
    }
    
    // Test test results structure
    if (!Array.isArray(mockStudentData.testResults) || mockStudentData.testResults.length === 0) {
      throw new Error('Test results should be a non-empty array');
    }
    
    // Test school data structure
    if (!mockSchoolData.schoolName || mockSchoolData.totalStudents !== mockSchoolData.students.length) {
      throw new Error('School data structure inconsistent');
    }
    
    console.log('✅ Data structure integrity test passed');
    return true;
  } catch (error) {
    console.error('❌ Data structure integrity test failed:', error);
    return false;
  }
}

/**
 * Test CSV export functionality
 */
export function testCsvExportFunctionality(): boolean {
  console.log('Testing CSV export functionality...');
  
  try {
    // Test export data conversion
    const exportData = convertUnifiedDataToExportFormat(
      mockSchoolData.students, 
      mockSchoolData.schoolName
    );
    
    if (!Array.isArray(exportData) || exportData.length === 0) {
      throw new Error('Export data conversion failed');
    }
    
    const firstRecord = exportData[0];
    if (!firstRecord.schoolName || !firstRecord.studentId || !firstRecord.email) {
      throw new Error('Export data missing required fields');
    }
    
    // Test CSV string generation
    const csvString = convertUnifiedExportDataToCSV(exportData);
    
    if (!csvString || csvString === 'No data available') {
      throw new Error('CSV string generation failed');
    }
    
    // Check if CSV contains expected headers
    const expectedHeaders = [
      'School Name',
      'Student ID', 
      'Student Name',
      'Email',
      'Total Tests Completed'
    ];
    
    const csvLines = csvString.split('\n');
    const headerLine = csvLines[0];
    
    for (const header of expectedHeaders) {
      if (!headerLine.includes(header)) {
        throw new Error(`CSV missing expected header: ${header}`);
      }
    }
    
    // Check if CSV contains data rows
    if (csvLines.length < 2) {
      throw new Error('CSV should contain at least header and one data row');
    }
    
    console.log('✅ CSV export functionality test passed');
    return true;
  } catch (error) {
    console.error('❌ CSV export functionality test failed:', error);
    return false;
  }
}

/**
 * Test demographic breakdown calculations
 */
export function testDemographicBreakdowns(): boolean {
  console.log('Testing demographic breakdown calculations...');
  
  try {
    const breakdown = mockSchoolData.demographicBreakdown;
    
    // Test country breakdown
    if (!breakdown.by_country['Ghana'] || breakdown.by_country['Ghana'] !== 1) {
      throw new Error('Country breakdown calculation incorrect');
    }
    
    // Test gender breakdown
    if (!breakdown.by_gender['Male'] || breakdown.by_gender['Male'] !== 1) {
      throw new Error('Gender breakdown calculation incorrect');
    }
    
    // Test role breakdown
    if (!breakdown.by_role['Student'] || breakdown.by_role['Student'] !== 1) {
      throw new Error('Role breakdown calculation incorrect');
    }
    
    // Test age breakdown
    if (!breakdown.by_age['20-24'] || breakdown.by_age['20-24'] !== 1) {
      throw new Error('Age breakdown calculation incorrect');
    }
    
    console.log('✅ Demographic breakdown test passed');
    return true;
  } catch (error) {
    console.error('❌ Demographic breakdown test failed:', error);
    return false;
  }
}

/**
 * Test test performance calculations
 */
export function testTestPerformanceCalculations(): boolean {
  console.log('Testing test performance calculations...');
  
  try {
    const testBreakdown = mockSchoolData.testBreakdown;
    
    // Test total tests count
    if (testBreakdown.totalTests !== 2) {
      throw new Error(`Expected 2 total tests, got ${testBreakdown.totalTests}`);
    }
    
    // Test pre-test count
    if (testBreakdown.preTests !== 1) {
      throw new Error(`Expected 1 pre-test, got ${testBreakdown.preTests}`);
    }
    
    // Test post-test count
    if (testBreakdown.postTests !== 1) {
      throw new Error(`Expected 1 post-test, got ${testBreakdown.postTests}`);
    }
    
    // Test average calculation
    if (testBreakdown.averageTestsPerStudent !== 2.0) {
      throw new Error(`Expected 2.0 average tests per student, got ${testBreakdown.averageTestsPerStudent}`);
    }
    
    console.log('✅ Test performance calculations test passed');
    return true;
  } catch (error) {
    console.error('❌ Test performance calculations test failed:', error);
    return false;
  }
}

/**
 * Run all unified analytics tests
 */
export function runUnifiedAnalyticsTests(): boolean {
  console.log('🧪 Running Unified Analytics System Tests...\n');
  
  const tests = [
    testDataStructureIntegrity,
    testCsvExportFunctionality,
    testDemographicBreakdowns,
    testTestPerformanceCalculations
  ];
  
  let passedTests = 0;
  let totalTests = tests.length;
  
  for (const test of tests) {
    try {
      if (test()) {
        passedTests++;
      }
    } catch (error) {
      console.error('Test execution error:', error);
    }
    console.log(''); // Add spacing between tests
  }
  
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All unified analytics tests passed!');
    return true;
  } else {
    console.log('⚠️  Some tests failed. Please review the implementation.');
    return false;
  }
}

// Export for use in other test files or manual testing
export {
  mockStudentData,
  mockSchoolData
};
