import { SchoolUnifiedData, UnifiedStudentData, UnifiedExportData, EnhancedUnifiedExportData } from '@/types/unified-analytics';

/**
 * Format date consistently for CSV export (YYYY-MM-DD HH:MM:SS format)
 */
function formatDateForCSV(dateString: string | undefined): string {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    // Format as YYYY-MM-DD HH:MM:SS for consistency
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.warn('Error formatting date:', dateString, error);
    return '';
  }
}

/**
 * Format date as date only (YYYY-MM-DD format)
 */
function formatDateOnlyForCSV(dateString: string | undefined): string {
  if (!dateString) return '';
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.warn('Error formatting date:', dateString, error);
    return '';
  }
}

/**
 * Safely handle missing data with consistent fallback values
 */
function handleMissingData(value: any, fallback: string = 'Not Provided'): string {
  if (value === null || value === undefined || value === '') {
    return fallback;
  }
  return String(value);
}

/**
 * Escape CSV values properly
 */
function escapeCSVValue(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }

  const stringValue = String(value);

  // If the value contains comma, quote, or newline, wrap in quotes and escape internal quotes
  if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n') || stringValue.includes('\r')) {
    return `"${stringValue.replace(/"/g, '""')}"`;
  }

  return stringValue;
}

/**
 * Convert unified student data to enhanced CSV export format with detailed test data
 */
export function convertUnifiedDataToEnhancedExportFormat(
  students: UnifiedStudentData[],
  schoolName: string
): EnhancedUnifiedExportData[] {
  const exportData: EnhancedUnifiedExportData[] = [];

  students.forEach(student => {
    const demographic = student.demographicData;
    const testResults = student.testResults;

    // Count test types
    const preTestsCompleted = testResults.filter(t => t.testType === 'pre_test').length;
    const postTestsCompleted = testResults.filter(t => t.testType === 'post_test').length;

    // Get test date ranges
    const testDates = testResults.map(t => new Date(t.submittedAt)).filter(d => !isNaN(d.getTime()));
    const earliestTestDate = testDates.length > 0 ? new Date(Math.min(...testDates.map(d => d.getTime()))) : undefined;
    const latestTestDate = testDates.length > 0 ? new Date(Math.max(...testDates.map(d => d.getTime()))) : undefined;

    // If student has no tests, create one row with demographic data only
    if (testResults.length === 0) {
      exportData.push({
        // Student Information
        schoolName: handleMissingData(schoolName, 'Unknown School'),
        studentId: handleMissingData(student.student.id),
        studentName: handleMissingData(student.student.fullName, 'Unknown Student'),
        email: handleMissingData(student.student.email),

        // Demographic Information
        demographicCompletedAt: formatDateForCSV(demographic?.completedAt),
        demographicCompletionStatus: demographic ? 'Completed' : 'Not Completed',
        consent: handleMissingData(demographic?.responses.consent),
        country: handleMissingData(demographic?.responses.country),
        gender: handleMissingData(demographic?.responses.gender),
        age: demographic?.responses.age ? String(demographic.responses.age) : 'Not Provided',
        formalTraining: handleMissingData(demographic?.responses.formal_training),
        roleType: handleMissingData(demographic?.responses.role_type),
        studentLevel: handleMissingData(demographic?.responses.student_level),
        university: handleMissingData(demographic?.responses.university),
        undergraduateProgram: handleMissingData(demographic?.responses.undergraduate_program),
        undergraduateYear: handleMissingData(demographic?.responses.undergraduate_year),
        postgraduateProgram: handleMissingData(demographic?.responses.postgraduate_program),
        practitionerWork: handleMissingData(demographic?.responses.practitioner_work),
        workplace: handleMissingData(demographic?.responses.workplace),
        location: handleMissingData(demographic?.responses.location),
        experienceYears: handleMissingData(demographic?.responses.experience_years),

        // Test Summary Information
        totalTestsCompleted: 0,
        preTestsCompleted: 0,
        postTestsCompleted: 0,
        earliestTestDate: '',
        latestTestDate: '',
        testCompletionStatus: 'No Tests Completed',

        // Individual Test Information (empty for students with no tests)
        testTitle: 'No Tests',
        testType: '',
        testTypeLabel: '',
        moduleTitle: '',
        courseTitle: '',
        testSubmittedAt: '',
        testResponseCount: '',
        testCompletionOrder: ''
      });
    } else {
      // Create one row for each test submission
      testResults
        .sort((a, b) => new Date(a.submittedAt).getTime() - new Date(b.submittedAt).getTime()) // Sort by submission date
        .forEach((test, index) => {
          exportData.push({
            // Student Information
            schoolName: handleMissingData(schoolName, 'Unknown School'),
            studentId: handleMissingData(student.student.id),
            studentName: handleMissingData(student.student.fullName, 'Unknown Student'),
            email: handleMissingData(student.student.email),

            // Demographic Information
            demographicCompletedAt: formatDateForCSV(demographic?.completedAt),
            demographicCompletionStatus: demographic ? 'Completed' : 'Not Completed',
            consent: handleMissingData(demographic?.responses.consent),
            country: handleMissingData(demographic?.responses.country),
            gender: handleMissingData(demographic?.responses.gender),
            age: demographic?.responses.age ? String(demographic.responses.age) : 'Not Provided',
            formalTraining: handleMissingData(demographic?.responses.formal_training),
            roleType: handleMissingData(demographic?.responses.role_type),
            studentLevel: handleMissingData(demographic?.responses.student_level),
            university: handleMissingData(demographic?.responses.university),
            undergraduateProgram: handleMissingData(demographic?.responses.undergraduate_program),
            undergraduateYear: handleMissingData(demographic?.responses.undergraduate_year),
            postgraduateProgram: handleMissingData(demographic?.responses.postgraduate_program),
            practitionerWork: handleMissingData(demographic?.responses.practitioner_work),
            workplace: handleMissingData(demographic?.responses.workplace),
            location: handleMissingData(demographic?.responses.location),
            experienceYears: handleMissingData(demographic?.responses.experience_years),

            // Test Summary Information
            totalTestsCompleted: testResults.length,
            preTestsCompleted,
            postTestsCompleted,
            earliestTestDate: formatDateForCSV(earliestTestDate?.toISOString()),
            latestTestDate: formatDateForCSV(latestTestDate?.toISOString()),
            testCompletionStatus: `${testResults.length} Test${testResults.length !== 1 ? 's' : ''} Completed`,

            // Individual Test Information
            testTitle: handleMissingData(test.testTitle, 'Unknown Test'),
            testType: handleMissingData(test.testType),
            testTypeLabel: test.testType === 'pre_test' ? 'Pre-Test' : test.testType === 'post_test' ? 'Post-Test' : handleMissingData(test.testType),
            moduleTitle: handleMissingData(test.moduleTitle, 'Unknown Module'),
            courseTitle: handleMissingData(test.courseTitle, 'Unknown Course'),
            testSubmittedAt: formatDateForCSV(test.submittedAt),
            testResponseCount: String(test.responseCount || 0),
            testCompletionOrder: String(index + 1)
          });
        });
    }
  });

  return exportData;
}

/**
 * Legacy function for backward compatibility
 */
export function convertUnifiedDataToExportFormat(
  students: UnifiedStudentData[],
  schoolName: string
): UnifiedExportData[] {
  return students.map(student => {
    const demographic = student.demographicData;
    const testResults = student.testResults;

    // Count test types
    const preTestsCompleted = testResults.filter(t => t.testType === 'pre_test').length;
    const postTestsCompleted = testResults.filter(t => t.testType === 'post_test').length;

    // Get latest test date
    const latestTestDate = testResults.length > 0
      ? testResults.sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())[0].submittedAt
      : undefined;

    return {
      schoolName,
      studentId: student.student.id,
      studentName: student.student.fullName,
      email: student.student.email,
      // Demographic fields
      demographicCompletedAt: demographic?.completedAt,
      consent: demographic?.responses.consent as string,
      country: demographic?.responses.country as string,
      gender: demographic?.responses.gender as string,
      age: demographic?.responses.age as number,
      formal_training: demographic?.responses.formal_training as string,
      role_type: demographic?.responses.role_type as string,
      student_level: demographic?.responses.student_level as string,
      university: demographic?.responses.university as string,
      undergraduate_program: demographic?.responses.undergraduate_program as string,
      undergraduate_year: demographic?.responses.undergraduate_year as string,
      postgraduate_program: demographic?.responses.postgraduate_program as string,
      practitioner_work: demographic?.responses.practitioner_work as string,
      workplace: demographic?.responses.workplace as string,
      location: demographic?.responses.location as string,
      experience_years: demographic?.responses.experience_years as string,
      // Test results summary
      totalTestsCompleted: student.totalTestsCompleted,
      preTestsCompleted,
      postTestsCompleted,
      latestTestDate,
      testDetails: JSON.stringify(testResults.map(t => ({
        testTitle: t.testTitle,
        testType: t.testType,
        moduleTitle: t.moduleTitle,
        courseTitle: t.courseTitle,
        submittedAt: t.submittedAt,
        responseCount: t.responseCount
      })))
    };
  });
}

/**
 * Convert enhanced unified export data to CSV string with detailed test information
 */
export function convertEnhancedUnifiedExportDataToCSV(exportData: EnhancedUnifiedExportData[]): string {
  if (!exportData || exportData.length === 0) {
    return 'No data available';
  }

  // Define comprehensive CSV headers with clear, descriptive names
  const headers = [
    // Student Information
    'School/University',
    'Student ID',
    'Student Name',
    'Email Address',

    // Demographic Information
    'Demographic Questionnaire Completed',
    'Demographic Completion Status',
    'Consent to Participate',
    'Country of Origin',
    'Gender',
    'Age',
    'Previous IV Cannulation Training',
    'Role (Student/Practitioner)',
    'Academic Level',
    'University/Institution',
    'Undergraduate Program',
    'Undergraduate Year',
    'Postgraduate Program',
    'Practitioner Specialization',
    'Workplace Type',
    'Work Location',
    'Years of Experience',

    // Test Summary Information
    'Total Tests Completed',
    'Pre-Tests Completed',
    'Post-Tests Completed',
    'First Test Date',
    'Most Recent Test Date',
    'Test Completion Status',

    // Individual Test Information
    'Test Title',
    'Test Type (Raw)',
    'Test Type (Label)',
    'Module Name',
    'Course Name',
    'Test Submission Date',
    'Number of Responses',
    'Test Completion Order'
  ];

  // Create CSV content with UTF-8 BOM for proper encoding
  const csvRows = [
    headers.join(','),
    ...exportData.map(row => {
      const values = [
        // Student Information
        escapeCSVValue(row.schoolName),
        escapeCSVValue(row.studentId),
        escapeCSVValue(row.studentName),
        escapeCSVValue(row.email),

        // Demographic Information
        escapeCSVValue(row.demographicCompletedAt),
        escapeCSVValue(row.demographicCompletionStatus),
        escapeCSVValue(row.consent),
        escapeCSVValue(row.country),
        escapeCSVValue(row.gender),
        escapeCSVValue(row.age),
        escapeCSVValue(row.formalTraining),
        escapeCSVValue(row.roleType),
        escapeCSVValue(row.studentLevel),
        escapeCSVValue(row.university),
        escapeCSVValue(row.undergraduateProgram),
        escapeCSVValue(row.undergraduateYear),
        escapeCSVValue(row.postgraduateProgram),
        escapeCSVValue(row.practitionerWork),
        escapeCSVValue(row.workplace),
        escapeCSVValue(row.location),
        escapeCSVValue(row.experienceYears),

        // Test Summary Information
        escapeCSVValue(row.totalTestsCompleted),
        escapeCSVValue(row.preTestsCompleted),
        escapeCSVValue(row.postTestsCompleted),
        escapeCSVValue(row.earliestTestDate),
        escapeCSVValue(row.latestTestDate),
        escapeCSVValue(row.testCompletionStatus),

        // Individual Test Information
        escapeCSVValue(row.testTitle),
        escapeCSVValue(row.testType),
        escapeCSVValue(row.testTypeLabel),
        escapeCSVValue(row.moduleTitle),
        escapeCSVValue(row.courseTitle),
        escapeCSVValue(row.testSubmittedAt),
        escapeCSVValue(row.testResponseCount),
        escapeCSVValue(row.testCompletionOrder)
      ];

      return values.join(',');
    })
  ];

  // Add UTF-8 BOM for proper encoding in Excel and other applications
  const BOM = '\uFEFF';
  return BOM + csvRows.join('\n');
}

/**
 * Legacy CSV conversion function for backward compatibility
 */
export function convertUnifiedExportDataToCSV(exportData: UnifiedExportData[]): string {
  if (!exportData || exportData.length === 0) {
    return 'No data available';
  }

  // Define CSV headers
  const headers = [
    'School Name',
    'Student ID',
    'Student Name',
    'Email',
    'Demographic Completed At',
    'Consent',
    'Country',
    'Gender',
    'Age',
    'Formal Training',
    'Role Type',
    'Student Level',
    'University',
    'Undergraduate Program',
    'Undergraduate Year',
    'Postgraduate Program',
    'Practitioner Work',
    'Workplace',
    'Location',
    'Experience Years',
    'Total Tests Completed',
    'Pre-Tests Completed',
    'Post-Tests Completed',
    'Latest Test Date',
    'Test Details (JSON)'
  ];

  // Create CSV content
  const csvContent = [
    headers.join(','),
    ...exportData.map(row => {
      const values = [
        row.schoolName,
        row.studentId,
        row.studentName,
        row.email,
        row.demographicCompletedAt ? new Date(row.demographicCompletedAt).toLocaleDateString() : '',
        row.consent || '',
        row.country || '',
        row.gender || '',
        row.age || '',
        row.formal_training || '',
        row.role_type || '',
        row.student_level || '',
        row.university || '',
        row.undergraduate_program || '',
        row.undergraduate_year || '',
        row.postgraduate_program || '',
        row.practitioner_work || '',
        row.workplace || '',
        row.location || '',
        row.experience_years || '',
        row.totalTestsCompleted,
        row.preTestsCompleted,
        row.postTestsCompleted,
        row.latestTestDate ? new Date(row.latestTestDate).toLocaleDateString() : '',
        `"${row.testDetails.replace(/"/g, '""')}"` // Escape quotes in JSON
      ];

      // Escape values that contain commas or quotes
      return values.map(value => {
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      }).join(',');
    })
  ].join('\n');

  return csvContent;
}

/**
 * Download enhanced unified analytics CSV for a specific school with detailed test data
 */
export function downloadSchoolUnifiedCSV(schoolData: SchoolUnifiedData): void {
  const exportData = convertUnifiedDataToEnhancedExportFormat(schoolData.students, schoolData.schoolName);
  const csvContent = convertEnhancedUnifiedExportDataToCSV(exportData);

  // Create descriptive filename with school name and timestamp
  const now = new Date();
  const timestamp = formatDateOnlyForCSV(now.toISOString());
  const timeStamp = now.toTimeString().slice(0, 5).replace(':', ''); // HHMM format
  const sanitizedSchoolName = schoolData.schoolName
    .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special characters but keep spaces
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 50); // Limit length for filename compatibility

  const filename = `${sanitizedSchoolName}_Detailed_Analytics_${timestamp}_${timeStamp}.csv`;

  // Create and download the file with proper encoding
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Download legacy format unified analytics CSV for a specific school
 */
export function downloadSchoolUnifiedCSVLegacy(schoolData: SchoolUnifiedData): void {
  const exportData = convertUnifiedDataToExportFormat(schoolData.students, schoolData.schoolName);
  const csvContent = convertUnifiedExportDataToCSV(exportData);

  // Create filename with school name and timestamp
  const timestamp = formatDateOnlyForCSV(new Date().toISOString());
  const sanitizedSchoolName = schoolData.schoolName.replace(/[^a-zA-Z0-9]/g, '_');
  const filename = `${sanitizedSchoolName}_Unified_Analytics_Legacy_${timestamp}.csv`;

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Download all unified analytics data as a single enhanced CSV file with detailed test data
 */
export function downloadAllUnifiedCSV(schools: SchoolUnifiedData[]): void {
  // Combine all students from all schools using enhanced format
  const allExportData: EnhancedUnifiedExportData[] = schools.flatMap(school =>
    convertUnifiedDataToEnhancedExportFormat(school.students, school.schoolName)
  );

  if (allExportData.length === 0) {
    console.warn('No data available for export');
    return;
  }

  const csvContent = convertEnhancedUnifiedExportDataToCSV(allExportData);

  // Create descriptive filename with timestamp
  const now = new Date();
  const timestamp = formatDateOnlyForCSV(now.toISOString());
  const timeStamp = now.toTimeString().slice(0, 5).replace(':', ''); // HHMM format
  const totalStudents = new Set(allExportData.map(row => row.studentId)).size;
  const totalSchools = schools.length;

  const filename = `All_Schools_Detailed_Analytics_${totalSchools}schools_${totalStudents}students_${timestamp}_${timeStamp}.csv`;

  // Create and download the file with proper encoding
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Download all unified analytics data as a single legacy CSV file
 */
export function downloadAllUnifiedCSVLegacy(schools: SchoolUnifiedData[]): void {
  // Combine all students from all schools using legacy format
  const allExportData: UnifiedExportData[] = schools.flatMap(school =>
    convertUnifiedDataToExportFormat(school.students, school.schoolName)
  );

  if (allExportData.length === 0) {
    return;
  }

  const csvContent = convertUnifiedExportDataToCSV(allExportData);

  // Create filename with timestamp
  const timestamp = formatDateOnlyForCSV(new Date().toISOString());
  const filename = `All_Schools_Unified_Analytics_Legacy_${timestamp}.csv`;

  // Create and download the file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}

/**
 * Download enhanced summary statistics CSV for all schools with improved formatting
 */
export function downloadSchoolSummaryCSV(schools: SchoolUnifiedData[]): void {
  if (!schools || schools.length === 0) {
    console.warn('No school data available for summary export');
    return;
  }

  const headers = [
    'School/University Name',
    'Total Students Enrolled',
    'Students with Demographics (%)',
    'Students with Test Results (%)',
    'Students with Complete Data (%)',
    'Overall Completion Rate (%)',
    'Total Test Submissions',
    'Pre-Test Submissions',
    'Post-Test Submissions',
    'Average Tests per Student',
    'Most Common Country',
    'Most Common Gender',
    'Most Common Role Type',
    'Most Common Age Group',
    'Data Quality Score (%)'
  ];

  const rows = schools.map(school => {
    const completionRate = school.totalStudents > 0
      ? Math.round((school.studentsWithBoth / school.totalStudents) * 100)
      : 0;

    const demographicsRate = school.totalStudents > 0
      ? Math.round((school.studentsWithDemographics / school.totalStudents) * 100)
      : 0;

    const testsRate = school.totalStudents > 0
      ? Math.round((school.studentsWithTests / school.totalStudents) * 100)
      : 0;

    // Calculate data quality score (average of demographics and tests completion)
    const dataQualityScore = Math.round((demographicsRate + testsRate) / 2);

    // Get top demographic values with better handling
    const topCountry = Object.entries(school.demographicBreakdown.by_country)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Not Available';
    const topGender = Object.entries(school.demographicBreakdown.by_gender)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Not Available';
    const topRole = Object.entries(school.demographicBreakdown.by_role)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Not Available';
    const topAge = Object.entries(school.demographicBreakdown.by_age)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'Not Available';

    const values = [
      school.schoolName,
      school.totalStudents,
      `${school.studentsWithDemographics} (${demographicsRate}%)`,
      `${school.studentsWithTests} (${testsRate}%)`,
      `${school.studentsWithBoth} (${completionRate}%)`,
      completionRate,
      school.testBreakdown.totalTests,
      school.testBreakdown.preTests,
      school.testBreakdown.postTests,
      school.testBreakdown.averageTestsPerStudent,
      topCountry,
      topGender,
      topRole,
      topAge,
      dataQualityScore
    ];

    return values.map(value => escapeCSVValue(value)).join(',');
  });

  // Add UTF-8 BOM and create content
  const BOM = '\uFEFF';
  const csvContent = BOM + [headers.join(','), ...rows].join('\n');

  // Create descriptive filename with timestamp and summary info
  const now = new Date();
  const timestamp = formatDateOnlyForCSV(now.toISOString());
  const timeStamp = now.toTimeString().slice(0, 5).replace(':', '');
  const totalSchools = schools.length;
  const totalStudents = schools.reduce((sum, school) => sum + school.totalStudents, 0);

  const filename = `School_Summary_Analytics_${totalSchools}schools_${totalStudents}students_${timestamp}_${timeStamp}.csv`;

  // Create and download the file with proper encoding
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
}
