/**
 * UNIFIED LESSON CONTENT STYLES
 * Single source of truth for all lesson content styling
 * Optimized for modern, clean, and simple design
 */

/* CSS Custom Properties - Streamlined Design System */
:root {
  /* Typography Scale - Optimized */
  --lesson-font-xs: 0.75rem;
  --lesson-font-sm: 0.875rem;
  --lesson-font-base: 1rem;
  --lesson-font-lg: 1.125rem;
  --lesson-font-xl: 1.25rem;
  --lesson-font-2xl: 1.5rem;
  --lesson-font-3xl: 1.875rem;
  --lesson-font-4xl: 2.25rem;

  /* Spacing Scale - Optimized for readability */
  --lesson-space-xs: 0.375rem;   /* 6px */
  --lesson-space-sm: 0.75rem;    /* 12px */
  --lesson-space-md: 1rem;       /* 16px */
  --lesson-space-lg: 1.25rem;    /* 20px */
  --lesson-space-xl: 1.5rem;     /* 24px */
  --lesson-space-2xl: 2rem;      /* 32px */
  --lesson-space-3xl: 2.5rem;    /* 40px */

  /* Line Heights */
  --lesson-line-tight: 1.25;
  --lesson-line-normal: 1.5;
  --lesson-line-relaxed: 1.6;

  /* Border Radius */
  --lesson-radius-sm: 0.375rem;
  --lesson-radius-md: 0.5rem;
  --lesson-radius-lg: 0.75rem;
}

/* LESSON CONTAINER - Modern and Clean */
.lesson-content-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  padding: var(--lesson-space-2xl) var(--lesson-space-xl);
  line-height: var(--lesson-line-relaxed);
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

.lesson-content-container.full-width {
  max-width: none;
  padding: var(--lesson-space-2xl) var(--lesson-space-xl);
}

.lesson-content-container.lesson-content-no-padding {
  padding: 0;
  max-width: none;
}

/* TYPOGRAPHY - Modern and Readable */
.lesson-prose {
  color: hsl(var(--foreground));
  font-size: var(--lesson-font-base);
  line-height: var(--lesson-line-relaxed);
  letter-spacing: -0.01em;
  max-width: none;
}

.lesson-prose h1 {
  font-size: var(--lesson-font-3xl);
  font-weight: 700;
  line-height: var(--lesson-line-tight);
  letter-spacing: -0.025em;
  margin: var(--lesson-space-3xl) 0 var(--lesson-space-xl);
  color: hsl(var(--foreground));
}

.lesson-prose h1:first-child {
  margin-top: 0;
}

.lesson-prose h2 {
  font-size: var(--lesson-font-2xl);
  font-weight: 600;
  line-height: var(--lesson-line-normal);
  letter-spacing: -0.02em;
  margin: var(--lesson-space-2xl) 0 var(--lesson-space-lg);
  color: hsl(var(--foreground));
}

.lesson-prose h3 {
  font-size: var(--lesson-font-xl);
  font-weight: 600;
  line-height: var(--lesson-line-normal);
  letter-spacing: -0.015em;
  margin: var(--lesson-space-xl) 0 var(--lesson-space-md);
  color: hsl(var(--foreground));
}

.lesson-prose h4,
.lesson-prose h5,
.lesson-prose h6 {
  font-size: var(--lesson-font-lg);
  font-weight: 600;
  line-height: var(--lesson-line-normal);
  margin: var(--lesson-space-xl) 0 var(--lesson-space-md);
  color: hsl(var(--foreground));
}

.lesson-prose p {
  margin: var(--lesson-space-lg) 0;
  line-height: var(--lesson-line-relaxed);
  text-align: justify;
  text-justify: inter-word;
}

.lesson-prose p:first-child {
  margin-top: 0;
}

.lesson-prose p:last-child {
  margin-bottom: 0;
}

/* LISTS - Clean and Readable with Proper Markers */
.lesson-prose ul,
.lesson-prose ol,
.markdown-preview ul,
.markdown-preview ol {
  margin: var(--lesson-space-lg) 0;
  padding-left: var(--lesson-space-2xl);
  list-style-position: outside;
}

.lesson-prose ul,
.markdown-preview ul {
  list-style-type: disc !important;
}

.lesson-prose ol,
.markdown-preview ol {
  list-style-type: decimal !important;
}

.lesson-prose li,
.markdown-preview li {
  margin: var(--lesson-space-md) 0;
  line-height: var(--lesson-line-relaxed);
  display: list-item !important;
}

.lesson-prose li p,
.markdown-preview li p {
  margin: var(--lesson-space-md) 0;
}

/* Nested lists */
.lesson-prose ul ul,
.markdown-preview ul ul {
  list-style-type: circle !important;
  margin: var(--lesson-space-sm) 0;
}

.lesson-prose ul ul ul,
.markdown-preview ul ul ul {
  list-style-type: square !important;
}

.lesson-prose ol ol,
.markdown-preview ol ol {
  list-style-type: lower-alpha !important;
  margin: var(--lesson-space-sm) 0;
}

.lesson-prose ol ol ol,
.markdown-preview ol ol ol {
  list-style-type: lower-roman !important;
}

/* TASK LISTS - Modern Styling */
.lesson-prose .task-list {
  list-style: none;
  padding-left: 0;
}

.lesson-prose .task-item {
  display: flex;
  align-items: flex-start;
  gap: var(--lesson-space-md);
  margin: var(--lesson-space-md) 0;
  list-style: none;
}

.lesson-prose .task-item input[type="checkbox"] {
  margin-top: 0.125rem;
  accent-color: hsl(var(--primary));
  flex-shrink: 0;
}

/* TABLES - Professional and Clean */
.lesson-prose table {
  width: 100%;
  border-collapse: collapse;
  margin: var(--lesson-space-2xl) 0;
  font-size: var(--lesson-font-sm);
  border: 1px solid hsl(var(--border));
  border-radius: var(--lesson-radius-lg);
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background-color: hsl(var(--card));
}

.lesson-prose th,
.lesson-prose td {
  padding: var(--lesson-space-md) var(--lesson-space-lg);
  text-align: left;
  border-bottom: 1px solid hsl(var(--border));
  vertical-align: top;
  line-height: var(--lesson-line-normal);
}

.lesson-prose th {
  background-color: hsl(var(--muted) / 0.8);
  font-weight: 600;
  font-size: var(--lesson-font-sm);
  letter-spacing: 0.025em;
  text-transform: uppercase;
  border-bottom: 2px solid hsl(var(--border));
  color: hsl(var(--foreground));
}

.lesson-prose td {
  background-color: hsl(var(--card));
  color: hsl(var(--foreground));
}

.lesson-prose tbody tr:nth-child(even) td {
  background-color: hsl(var(--muted) / 0.2);
}

.lesson-prose tbody tr:hover td {
  background-color: hsl(var(--muted) / 0.4);
  transition: background-color 0.15s ease;
}

.lesson-prose tbody tr:last-child td {
  border-bottom: none;
}

/* TABLE WRAPPER - Responsive Scrolling */
.table-wrapper {
  overflow-x: auto;
  border-radius: var(--lesson-radius-lg);
  margin: var(--lesson-space-lg) 0;
  -webkit-overflow-scrolling: touch;
}

.table-wrapper table {
  margin: 0;
  border-radius: 0;
}

/* CODE BLOCKS - Clean and Readable */
.lesson-prose pre {
  background-color: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border));
  border-radius: var(--lesson-radius-md);
  padding: var(--lesson-space-xl);
  margin: var(--lesson-space-2xl) 0;
  overflow-x: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: var(--lesson-font-sm);
  line-height: var(--lesson-line-normal);
}

.lesson-prose code {
  background-color: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border));
  border-radius: var(--lesson-radius-sm);
  padding: 0.125rem 0.25rem;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875em;
}

.lesson-prose pre code {
  background: none;
  border: none;
  padding: 0;
}

/* BLOCKQUOTES - Elegant Styling */
.lesson-prose blockquote {
  border-left: 4px solid hsl(var(--primary));
  margin: var(--lesson-space-2xl) 0;
  padding: var(--lesson-space-xl) var(--lesson-space-2xl);
  background-color: hsl(var(--muted) / 0.2);
  border-radius: 0 var(--lesson-radius-md) var(--lesson-radius-md) 0;
  font-style: italic;
}

.lesson-prose blockquote p {
  margin: var(--lesson-space-md) 0;
}

.lesson-prose blockquote p:first-child {
  margin-top: 0;
}

.lesson-prose blockquote p:last-child {
  margin-bottom: 0;
}

/* LINKS - Accessible and Clear */
.lesson-prose a {
  color: hsl(var(--primary));
  text-decoration: underline;
  text-underline-offset: 0.125em;
  transition: color 0.15s ease;
}

.lesson-prose a:hover {
  color: hsl(var(--primary) / 0.8);
}

/* IMAGES - Responsive and Clean */
.lesson-prose img {
  max-width: 100%;
  height: auto;
  border-radius: var(--lesson-radius-md);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: var(--lesson-space-2xl) auto;
  display: block;
}

/* HORIZONTAL RULES */
.lesson-prose hr {
  border: none;
  height: 1px;
  background-color: hsl(var(--border));
  margin: var(--lesson-space-3xl) 0;
}

/* ACCORDION STYLES - Modern and Clean */
.lesson-accordion .lesson-accordion-item {
  border: 1px solid hsl(var(--border));
  border-radius: var(--lesson-radius-md);
  margin: var(--lesson-space-md) 0;
  overflow: hidden;
}

.lesson-accordion .lesson-accordion-trigger {
  padding: var(--lesson-space-lg);
  background-color: hsl(var(--muted) / 0.3);
  border: none;
  width: 100%;
  text-align: left;
  font-weight: 600;
  transition: background-color 0.15s ease;
}

.lesson-accordion .lesson-accordion-trigger:hover {
  background-color: hsl(var(--muted) / 0.5);
}

.lesson-accordion .lesson-accordion-content {
  padding: var(--lesson-space-lg);
  background-color: hsl(var(--card));
}

/* RESPONSIVE DESIGN */
@media (min-width: 768px) {
  .lesson-content-container {
    padding: var(--lesson-space-3xl) var(--lesson-space-2xl);
  }

  .lesson-prose {
    font-size: var(--lesson-font-lg);
  }

  .lesson-prose h1 {
    font-size: var(--lesson-font-4xl);
  }

  .lesson-prose h2 {
    font-size: var(--lesson-font-3xl);
  }

  .lesson-prose h3 {
    font-size: var(--lesson-font-2xl);
  }

  /* Enhanced spacing for desktop */
  .lesson-prose p {
    margin: var(--lesson-space-xl) 0;
  }

  .lesson-prose ul,
  .lesson-prose ol {
    margin: var(--lesson-space-xl) 0;
  }

  .lesson-prose li {
    margin: var(--lesson-space-md) 0;
  }
}

@media (max-width: 640px) {
  .lesson-content-container {
    padding: var(--lesson-space-lg) var(--lesson-space-md);
  }

  .lesson-prose {
    font-size: var(--lesson-font-sm);
  }

  .lesson-prose h1 {
    font-size: var(--lesson-font-2xl);
    margin: var(--lesson-space-2xl) 0 var(--lesson-space-lg);
  }

  .lesson-prose h2 {
    font-size: var(--lesson-font-xl);
    margin: var(--lesson-space-xl) 0 var(--lesson-space-md);
  }

  .lesson-prose h3 {
    font-size: var(--lesson-font-lg);
    margin: var(--lesson-space-lg) 0 var(--lesson-space-sm);
  }

  .lesson-prose table {
    font-size: var(--lesson-font-xs);
    margin: var(--lesson-space-xl) 0;
  }

  .lesson-prose th,
  .lesson-prose td {
    padding: var(--lesson-space-sm) var(--lesson-space-md);
  }

  /* Maintain readable spacing on mobile */
  .lesson-prose p {
    margin: var(--lesson-space-md) 0;
    text-align: left; /* Remove justify on mobile for better readability */
  }

  .lesson-prose ul,
  .lesson-prose ol {
    margin: var(--lesson-space-md) 0;
  }

  .lesson-prose li {
    margin: var(--lesson-space-sm) 0;
  }
}

/* DARK MODE COMPATIBILITY */
.dark .lesson-prose img {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.dark .lesson-prose pre {
  background-color: hsl(var(--muted) / 0.5);
}

.dark .lesson-prose code {
  background-color: hsl(var(--muted) / 0.5);
}

/* LAYOUT UTILITIES */
.lesson-content-wrapper-full-width {
  width: 100%;
  padding: 0;
}

.prose-content-area-full-width {
  width: 100%;
  max-width: none;
}

.no-max-width {
  max-width: none !important;
}

/* FORCE LIST STYLES - Override Tailwind CSS resets */
.lesson-content-container ul,
.lesson-content-container ol,
.lesson-prose ul,
.lesson-prose ol,
.markdown-preview ul,
.markdown-preview ol {
  list-style: revert !important;
  padding-left: var(--lesson-space-2xl) !important;
  margin: var(--lesson-space-lg) 0 !important;
}

.lesson-content-container ul,
.lesson-prose ul,
.markdown-preview ul {
  list-style-type: disc !important;
}

.lesson-content-container ol,
.lesson-prose ol,
.markdown-preview ol {
  list-style-type: decimal !important;
}

.lesson-content-container li,
.lesson-prose li,
.markdown-preview li {
  display: list-item !important;
  margin: var(--lesson-space-md) 0 !important;
  padding-left: 0 !important;
  line-height: var(--lesson-line-relaxed) !important;
}

/* Ensure nested lists work properly */
.lesson-content-container ul ul,
.lesson-prose ul ul,
.markdown-preview ul ul {
  list-style-type: circle !important;
  margin: var(--lesson-space-sm) 0 !important;
}

.lesson-content-container ol ol,
.lesson-prose ol ol,
.markdown-preview ol ol {
  list-style-type: lower-alpha !important;
  margin: var(--lesson-space-sm) 0 !important;
}

/* ENHANCED LESSON CONTENT STYLES - Modern and Clean UI */

/* Enhanced main container */
.enhanced-lesson-container {
  width: 100%;
  max-width: 100%;
  margin: 0;
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  padding: var(--lesson-space-2xl) var(--lesson-space-xl);
  line-height: var(--lesson-line-relaxed);
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Enhanced video container with improved responsiveness */
.enhanced-video-container {
  position: relative;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.enhanced-video-container .video-wrapper {
  position: relative;
  width: 100%;
  background: linear-gradient(135deg, hsl(var(--muted)/0.1) 0%, hsl(var(--muted)/0.05) 100%);
  transition: all 0.3s ease;
  /* Ensure proper aspect ratio maintenance */
  aspect-ratio: 16 / 9;
}

.enhanced-video-container .video-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.enhanced-video-container iframe,
.enhanced-video-container video {
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Enhanced responsive video sizing with unified system compatibility */
@media (max-width: 768px) {
  .enhanced-video-container .video-wrapper {
    aspect-ratio: var(--video-aspect-ratio-mobile, 16 / 10);
    border-radius: 0.5rem;
  }

  .enhanced-video-container iframe,
  .enhanced-video-container video {
    border-radius: 0.5rem;
  }

  .enhanced-video-container:hover {
    transform: none; /* Disable hover effects on mobile */
  }
}

@media (max-width: 480px) {
  .enhanced-video-container .video-wrapper {
    aspect-ratio: 16 / 11; /* Slightly taller for very small screens */
    border-radius: 0.375rem;
  }

  .enhanced-video-container iframe,
  .enhanced-video-container video {
    border-radius: 0.375rem;
  }
}

@media (min-width: 1200px) {
  .enhanced-video-container {
    max-width: 900px; /* Limit max width on very large screens */
  }
}

/* Video loading state */
.enhanced-video-container .video-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, hsl(var(--muted)/0.1) 50%, transparent 70%);
  background-size: 200% 200%;
  animation: shimmer 2s infinite;
  border-radius: 0.75rem;
  z-index: 1;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-video-container .video-wrapper.loading::before {
  opacity: 1;
}

@keyframes shimmer {
  0% {
    background-position: -200% -200%;
  }
  100% {
    background-position: 200% 200%;
  }
}

/* Enhanced prose content wrapper */
.lesson-prose-enhanced {
  color: hsl(var(--foreground));
  font-size: var(--lesson-font-base);
  line-height: var(--lesson-line-relaxed);
  letter-spacing: -0.01em;
  max-width: none;
}

.prose-content-wrapper {
  background-color: hsl(var(--card)/0.3);
  border: 1px solid hsl(var(--border)/0.2);
  border-radius: 1rem;
  padding: var(--lesson-space-2xl);
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
}

.prose-content-wrapper:hover {
  background-color: hsl(var(--card)/0.5);
  border-color: hsl(var(--border)/0.3);
}

/* Enhanced references section */
.enhanced-references-section {
  margin-top: var(--lesson-space-3xl);
}

.enhanced-lesson-accordion {
  border: none;
  background: none;
}

.enhanced-accordion-item {
  background: linear-gradient(135deg, hsl(var(--card)/0.8) 0%, hsl(var(--card)/0.4) 100%);
  backdrop-filter: blur(12px);
  transition: all 0.3s ease;
}

.enhanced-accordion-item:hover {
  background: linear-gradient(135deg, hsl(var(--card)/0.9) 0%, hsl(var(--card)/0.6) 100%);
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.enhanced-accordion-trigger {
  border: none;
  background: none;
  font-weight: 600;
  text-align: left;
  width: 100%;
  transition: all 0.2s ease;
}

.enhanced-accordion-trigger:hover {
  background-color: hsl(var(--muted)/0.3);
}

.enhanced-accordion-content {
  background: none;
  border-top: 1px solid hsl(var(--border)/0.2);
}

/* Responsive enhancements */
@media (min-width: 768px) {
  .enhanced-lesson-container {
    padding: var(--lesson-space-3xl) var(--lesson-space-2xl);
  }

  .prose-content-wrapper {
    padding: var(--lesson-space-3xl);
  }

  .enhanced-video-container .video-wrapper:hover {
    transform: translateY(-4px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  }
}

@media (max-width: 640px) {
  .enhanced-lesson-container {
    padding: var(--lesson-space-lg) var(--lesson-space-md);
  }

  .prose-content-wrapper {
    padding: var(--lesson-space-lg);
    border-radius: 0.75rem;
  }

  .enhanced-accordion-trigger {
    padding: var(--lesson-space-lg) var(--lesson-space-md);
  }

  .enhanced-accordion-content {
    padding: var(--lesson-space-md) var(--lesson-space-md) var(--lesson-space-lg);
  }
}

/* Dark mode enhancements */
.dark .enhanced-video-container .video-wrapper {
  background: linear-gradient(135deg, hsl(var(--muted)/0.2) 0%, hsl(var(--muted)/0.1) 100%);
}

.dark .prose-content-wrapper {
  background-color: hsl(var(--card)/0.4);
  border-color: hsl(var(--border)/0.3);
}

.dark .enhanced-accordion-item {
  background: linear-gradient(135deg, hsl(var(--card)/0.6) 0%, hsl(var(--card)/0.3) 100%);
}

.dark .enhanced-accordion-item:hover {
  background: linear-gradient(135deg, hsl(var(--card)/0.8) 0%, hsl(var(--card)/0.5) 100%);
}

/* Additional responsive video utilities */
.enhanced-video-container iframe[src*="youtube.com"],
.enhanced-video-container iframe[src*="youtu.be"],
.enhanced-video-container iframe[src*="vimeo.com"] {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 0;
  border-radius: 0.75rem;
}

/* Ensure video containers maintain aspect ratio */
.enhanced-video-container .video-wrapper.aspect-video {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
}

.enhanced-video-container .video-wrapper.aspect-video iframe,
.enhanced-video-container .video-wrapper.aspect-video video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

/* Video placeholder and loading states */
.enhanced-video-container .video-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, hsl(var(--muted)/0.1) 0%, hsl(var(--muted)/0.05) 100%);
  border-radius: 0.75rem;
  min-height: 200px;
}

.enhanced-video-container .video-error {
  background: linear-gradient(135deg, hsl(var(--destructive)/0.1) 0%, hsl(var(--destructive)/0.05) 100%);
  border: 1px solid hsl(var(--destructive)/0.2);
  color: hsl(var(--destructive));
}

/* Improved focus states for accessibility */
.enhanced-video-container iframe:focus,
.enhanced-video-container video:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .enhanced-video-container {
    display: none;
  }

  .enhanced-video-container::after {
    content: "Video content available in digital version";
    display: block;
    text-align: center;
    padding: 2rem;
    background: hsl(var(--muted)/0.1);
    border: 1px solid hsl(var(--border));
    border-radius: 0.5rem;
    font-style: italic;
    color: hsl(var(--muted-foreground));
  }
}
