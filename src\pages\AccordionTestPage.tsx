import React from 'react';
import Layout from '../components/Layout';
import LessonContent from '@/components/course/LessonContent';
import { PageContainer } from '@/components/ui/floating-sidebar-container';

const AccordionTestPage = () => {
  // Test content with accordion syntax
  const testContent = `# Accordion Functionality Test

This page tests the new accordion functionality for lesson content.

## Regular Content

This is regular paragraph content that should display normally.

## Accordion Examples

Here are some examples of accordion/collapsible sections:

(dropdown)<
### Additional Information

This content is hidden by default and will be revealed when the user clicks on the accordion trigger.

- This is a list item inside the accordion
- Another list item
- And one more

**Bold text** and *italic text* work inside accordions too!
>

## More Content

Regular content continues here.

(dropdown)<
### Technical Details

This is another accordion section with different content.

\`\`\`javascript
// Code blocks work inside accordions
function example() {
  console.log("Hello from inside an accordion!");
  return "This is working!";
}
\`\`\`

You can include any markdown content inside accordion sections:

1. Numbered lists
2. Code blocks
3. Images (if you have URLs)
4. Links: [Example Link](https://example.com)
>

## Final Section

(dropdown)<
### FAQ Section

**Q: How do accordions work?**
A: Accordions allow you to hide content by default and reveal it when clicked.

**Q: Can I nest content inside accordions?**
A: Yes! You can include any markdown content inside accordion sections.

**Q: Are accordions mobile-friendly?**
A: Yes, the accordion styling is responsive and works well on mobile devices.
>

This concludes the accordion test content. All accordion sections should be closed by default when the page loads.`;

  return (
    <Layout>
      <PageContainer pageType="default">
        <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <h2 className="text-lg font-semibold mb-2 text-blue-800 dark:text-blue-200">
            🎨 Accordion Functionality Test
          </h2>
          <p className="text-blue-700 dark:text-blue-300 text-sm">
            This page demonstrates the new accordion functionality for lesson content. 
            All accordion sections should be closed by default and expand when clicked.
          </p>
        </div>
        
        <LessonContent content={testContent} />
      </PageContainer>
    </Layout>
  );
};

export default AccordionTestPage;
